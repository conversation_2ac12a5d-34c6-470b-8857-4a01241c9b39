/* Blog-specific styles */

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(135deg, var(--raisin-black-1) 0%, var(--raisin-black-2) 100%);
    padding: 150px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.blog-hero-content {
    position: relative;
    z-index: 2;
}

.blog-hero-title {
    font-family: var(--ff-o<PERSON><PERSON>);
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--white);
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.blog-hero-subtitle {
    font-size: clamp(1rem, 2vw, 1.2rem);
    color: var(--light-gray);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.blog-hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--light-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* CTF Categories */
.ctf-categories {
    padding: 100px 0;
    background: var(--raisin-black-2);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.category-card {
    background: var(--raisin-black-1);
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 165, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: var(--orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.category-icon ion-icon {
    font-size: 2.5rem;
    color: var(--white);
}

.category-card:hover .category-icon {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.5);
}

.category-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.category-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.challenge-count {
    display: inline-block;
    background: var(--orange);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Methodology Section */
.methodology-section {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

.methodology-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.step-card {
    background: var(--raisin-black-2);
    padding: 40px 30px;
    border-radius: 12px;
    position: relative;
    border-left: 4px solid var(--orange);
    transition: all 0.3s ease;
}

.step-card:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.step-number {
    position: absolute;
    top: -15px;
    left: 30px;
    background: var(--orange);
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--ff-oswald);
    font-weight: 700;
    font-size: 1.2rem;
}

.step-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 15px;
    margin-top: 10px;
    text-transform: uppercase;
}

.step-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.step-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-tag {
    background: var(--raisin-black-3);
    color: var(--orange);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--orange);
}

/* Tools Section */
.tools-section {
    padding: 100px 0;
    background: var(--raisin-black-2);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.tool-category {
    background: var(--raisin-black-1);
    padding: 40px 30px;
    border-radius: 12px;
    border-top: 4px solid var(--orange);
}

.tool-category h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 25px;
    text-transform: uppercase;
    text-align: center;
}

.tool-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tool-item {
    background: var(--raisin-black-2);
    padding: 20px;
    border-radius: 8px;
    border-left: 3px solid var(--orange);
    transition: all 0.3s ease;
}

.tool-item:hover {
    background: var(--raisin-black-3);
    transform: translateX(5px);
}

.tool-name {
    display: block;
    font-weight: 600;
    color: var(--orange);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.tool-desc {
    color: var(--light-gray);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Challenges Section */
.challenges-section {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

.challenge-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin: 40px 0;
}

.filter-btn {
    background: var(--raisin-black-2);
    color: var(--light-gray);
    border: 2px solid var(--raisin-black-3);
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--orange);
    color: var(--white);
    border-color: var(--orange);
    transform: translateY(-2px);
}

.search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto 40px;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    background: var(--raisin-black-2);
    border: 2px solid var(--raisin-black-3);
    border-radius: 25px;
    color: var(--white);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--orange);
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);
}

.search-input::placeholder {
    color: var(--light-gray);
}

.search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--orange);
    font-size: 1.2rem;
}

/* Challenge Statistics */
.challenge-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 40px 0;
}

.stat-card {
    background: var(--raisin-black-2);
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: var(--orange);
    transform: translateY(-3px);
}

.stat-value {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 8px;
}

.stat-label {
    color: var(--light-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Difficulty Filters */
.difficulty-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.diff-filter-btn {
    background: var(--raisin-black-2);
    color: var(--light-gray);
    border: 2px solid var(--raisin-black-3);
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.diff-filter-btn:hover,
.diff-filter-btn.active {
    background: var(--orange);
    color: var(--white);
    border-color: var(--orange);
    transform: translateY(-1px);
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--raisin-black-1);
    border: 2px solid var(--orange);
    border-top: none;
    border-radius: 0 0 12px 12px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 10;
    display: none;
}

.suggestion-item {
    padding: 12px 20px;
    cursor: pointer;
    transition: background 0.2s ease;
    color: var(--light-gray);
}

.suggestion-item:hover {
    background: var(--raisin-black-2);
    color: var(--orange);
}

.challenges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 30px;
    margin-top: 40px;
    min-height: 400px;
}

.challenge-card {
    background: var(--raisin-black-2);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
}

.challenge-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.challenge-card.completed {
    border-color: #4CAF50;
}

.challenge-card.completed::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.challenge-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.challenge-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.bookmark-btn {
    background: none;
    border: none;
    color: var(--light-gray);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bookmark-btn:hover {
    color: var(--orange);
    background: rgba(255, 165, 0, 0.1);
}

.bookmark-btn.bookmarked {
    color: var(--orange);
}

.completed-badge {
    color: #4CAF50;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.challenge-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 15px;
}

.tool-tag-small {
    background: var(--raisin-black-3);
    color: var(--orange);
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid rgba(255, 165, 0, 0.3);
}

.challenge-stats {
    display: flex;
    gap: 15px;
    align-items: center;
}

.progress-bar-small {
    width: 60px;
    height: 4px;
    background: var(--raisin-black-3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill-small {
    height: 100%;
    background: linear-gradient(90deg, var(--orange), #e67e22);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.challenge-header {
    padding: 25px;
    border-bottom: 1px solid var(--raisin-black-3);
}

.challenge-title {
    font-family: var(--ff-oswald);
    font-size: 1.3rem;
    color: var(--white);
    margin-bottom: 10px;
    text-transform: uppercase;
}

.challenge-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.challenge-category {
    background: var(--orange);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.challenge-difficulty {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy { background: #4CAF50; color: white; }
.difficulty-medium { background: #FF9800; color: white; }
.difficulty-hard { background: #F44336; color: white; }

.challenge-description {
    color: var(--light-gray);
    line-height: 1.5;
    font-size: 0.9rem;
}

.challenge-footer {
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.challenge-points {
    font-weight: 600;
    color: var(--orange);
    font-size: 1.1rem;
}

.challenge-solves {
    color: var(--light-gray);
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--raisin-black-1);
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    border: 2px solid var(--orange);
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 25px;
    color: var(--light-gray);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--orange);
}

.modal-body {
    padding: 40px;
}

/* Code Block Styles */
.code-block {
    background: var(--raisin-black-3);
    border: 1px solid var(--raisin-black-2);
    border-radius: 8px;
    margin: 20px 0;
    position: relative;
}

.code-header {
    background: var(--raisin-black-2);
    padding: 10px 15px;
    border-bottom: 1px solid var(--raisin-black-1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-language {
    color: var(--orange);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.copy-btn {
    background: var(--orange);
    color: var(--white);
    border: none;
    padding: 5px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #e67e22;
    transform: scale(1.05);
}

.code-content {
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--light-gray);
    overflow-x: auto;
}

/* Enhanced Footer Styles */
.footer {
    background: linear-gradient(135deg, var(--raisin-black-1) 0%, #0a0a0a 100%);
    color: var(--light-gray);
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--orange), transparent);
}

/* Footer Top Section */
.footer-top {
    padding: 60px 0;
    text-align: center;
    border-bottom: 1px solid var(--raisin-black-3);
}

.footer-cta h2 {
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.footer-cta p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.footer-cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer Main Content */
.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 40px;
    padding: 80px 0 60px;
    border-bottom: 1px solid var(--raisin-black-3);
}

/* Footer Brand */
.footer-brand {
    max-width: 400px;
}

.footer-description {
    line-height: 1.7;
    margin: 20px 0 30px;
    color: var(--light-gray);
}

.footer-stats {
    display: flex;
    gap: 30px;
    margin: 30px 0;
    padding: 20px 0;
    border-top: 1px solid var(--raisin-black-3);
    border-bottom: 1px solid var(--raisin-black-3);
}

.footer-stat {
    text-align: center;
}

.footer-stat .stat-number {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 5px;
}

.footer-stat .stat-label {
    font-size: 0.9rem;
    color: var(--light-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Social Links */
.social-links h5 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.social-icons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--light-gray);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.social-link:hover {
    color: var(--orange);
    background: rgba(255, 165, 0, 0.1);
    border-color: var(--orange);
    transform: translateX(5px);
}

.social-link ion-icon {
    font-size: 1.2rem;
}

/* Footer Links */
.footer-links h4,
.footer-categories h4,
.footer-resources h4,
.footer-contact h4 {
    color: var(--white);
    font-family: var(--ff-oswald);
    font-size: 1.2rem;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    padding-bottom: 10px;
}

.footer-links h4::after,
.footer-categories h4::after,
.footer-resources h4::after,
.footer-contact h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--orange);
}

.footer-links ul,
.footer-categories ul,
.footer-resources ul {
    list-style: none;
    padding: 0;
}

.footer-links li,
.footer-categories li,
.footer-resources li {
    margin-bottom: 12px;
}

.footer-links a,
.footer-categories a,
.footer-resources a {
    color: var(--light-gray);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.footer-links a:hover,
.footer-categories a:hover,
.footer-resources a:hover {
    color: var(--orange);
    transform: translateX(5px);
}

.footer-links a ion-icon,
.footer-categories a ion-icon,
.footer-resources a ion-icon {
    font-size: 1rem;
    opacity: 0.7;
}

/* Contact Info */
.contact-info {
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border-left: 3px solid var(--orange);
}

.contact-item ion-icon {
    font-size: 1.3rem;
    color: var(--orange);
    margin-top: 2px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-label {
    font-size: 0.8rem;
    color: var(--orange);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.contact-details a {
    color: var(--light-gray);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-details a:hover {
    color: var(--orange);
}

/* Footer Newsletter */
.footer-newsletter {
    margin-top: 30px;
    padding: 20px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border: 1px solid var(--raisin-black-3);
}

.footer-newsletter h5 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 10px;
    text-transform: uppercase;
}

.footer-newsletter p {
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.newsletter-form-footer {
    display: flex;
    gap: 10px;
}

.newsletter-form-footer input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--raisin-black-3);
    border-radius: 6px;
    background: var(--raisin-black-1);
    color: var(--white);
    font-size: 0.9rem;
}

.newsletter-form-footer input:focus {
    outline: none;
    border-color: var(--orange);
}

.newsletter-form-footer button {
    padding: 10px 15px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Footer Middle Section */
.footer-middle {
    padding: 40px 0;
    border-bottom: 1px solid var(--raisin-black-3);
}

.footer-certifications h4 {
    color: var(--white);
    font-family: var(--ff-oswald);
    text-align: center;
    margin-bottom: 25px;
    text-transform: uppercase;
}

.cert-logos {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.cert-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border: 1px solid var(--raisin-black-3);
    transition: all 0.3s ease;
}

.cert-item:hover {
    border-color: var(--orange);
    transform: translateY(-3px);
}

.cert-item ion-icon {
    font-size: 2rem;
    color: var(--orange);
}

.cert-item span {
    font-size: 0.8rem;
    color: var(--light-gray);
    text-align: center;
    font-weight: 600;
}

/* Footer Bottom */
.footer-bottom {
    padding: 30px 0;
}

.footer-bottom-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
}

.footer-bottom-left p {
    margin: 0;
    font-size: 0.9rem;
}

.footer-version {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 5px !important;
}

.footer-badges {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: var(--raisin-black-2);
    border-radius: 15px;
    font-size: 0.8rem;
    color: var(--light-gray);
    border: 1px solid var(--raisin-black-3);
}

.badge-item ion-icon {
    color: var(--orange);
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.footer-bottom-links a {
    color: var(--light-gray);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
    color: var(--orange);
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-hero {
        padding: 120px 0 80px;
    }
    
    .blog-hero-stats {
        gap: 20px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .categories-grid,
    .methodology-steps,
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .challenge-filters {
        gap: 10px;
    }
    
    .filter-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .challenges-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .blog-hero-title {
        font-size: 2rem;
    }

    .blog-hero-subtitle {
        font-size: 0.9rem;
    }

    .category-card,
    .step-card,
    .tool-category {
        padding: 25px 20px;
    }

    .challenge-header,
    .challenge-footer {
        padding: 20px;
    }

    /* Footer Responsive */
    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-cta h2 {
        font-size: 2rem;
    }

    .footer-cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-stats {
        justify-content: center;
        gap: 20px;
    }

    .social-icons {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cert-logos {
        gap: 20px;
    }

    .footer-bottom-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }

    .footer-badges {
        justify-content: center;
    }

    .footer-bottom-links {
        justify-content: center;
        gap: 15px;
    }
}

@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .footer-brand {
        grid-column: 1 / -1;
        text-align: center;
        max-width: none;
    }
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--raisin-black-1);
    color: var(--white);
    padding: 15px 20px;
    border-radius: 8px;
    border-left: 4px solid var(--orange);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 10000;
    max-width: 350px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left-color: #4CAF50;
}

.toast-error {
    border-left-color: #F44336;
}

.toast-warning {
    border-left-color: #FF9800;
}

.toast-info {
    border-left-color: var(--orange);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.toast-content ion-icon {
    font-size: 1.2rem;
    color: var(--orange);
}

.toast-success .toast-content ion-icon {
    color: #4CAF50;
}

.toast-error .toast-content ion-icon {
    color: #F44336;
}

.toast-warning .toast-content ion-icon {
    color: #FF9800;
}

.toast-message {
    font-size: 0.9rem;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--light-gray);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.toast-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: var(--white);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--raisin-black-3);
    border-top: 4px solid var(--orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    font-size: 1.1rem;
    margin: 0;
    color: var(--light-gray);
}

/* Floating Action Buttons */
.floating-actions {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--orange);
    color: var(--white);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 20px rgba(255, 165, 0, 0.3);
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 165, 0, 0.4);
}

.main-fab {
    background: var(--orange);
    transform: rotate(0deg);
}

.main-fab.active {
    transform: rotate(45deg);
}

.fab-menu {
    display: flex;
    flex-direction: column-reverse;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.fab-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-menu .fab {
    width: 48px;
    height: 48px;
    font-size: 1.2rem;
    background: var(--raisin-black-2);
    border: 2px solid var(--orange);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--orange);
    color: var(--white);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 999;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* Progress Modal Styles */
.progress-overview {
    margin-bottom: 30px;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.progress-stat {
    text-align: center;
    padding: 20px;
    background: var(--raisin-black-2);
    border-radius: 8px;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.progress-stat:hover {
    border-color: var(--orange);
}

.progress-number {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 2rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 5px;
}

.progress-label {
    color: var(--light-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.category-progress-item {
    background: var(--raisin-black-2);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid var(--orange);
}

.category-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.category-name {
    font-weight: 600;
    color: var(--white);
    text-transform: capitalize;
}

.category-percentage {
    color: var(--orange);
    font-weight: 600;
}

.category-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--raisin-black-3);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.category-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--orange), #e67e22);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.category-progress-stats {
    font-size: 0.8rem;
    color: var(--light-gray);
}

/* Settings Modal Styles */
.settings-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--raisin-black-3);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 20px;
    text-transform: uppercase;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 0;
}

.setting-item label {
    color: var(--light-gray);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
}

.setting-item select,
.setting-item input[type="range"] {
    background: var(--raisin-black-2);
    border: 1px solid var(--raisin-black-3);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 4px;
}

.setting-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--orange);
}

/* Animation Classes */
.fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated {
    animation-fill-mode: both;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .floating-actions {
        bottom: 20px;
        right: 20px;
    }

    .back-to-top {
        bottom: 20px;
        left: 20px;
    }

    .progress-stats {
        grid-template-columns: 1fr;
    }
}

/* Advanced Features */
.challenge-info {
    display: flex;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.challenge-info .challenge-category,
.challenge-info .challenge-difficulty,
.challenge-info .challenge-points {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.challenge-info .challenge-points {
    background: var(--orange);
    color: var(--white);
}

.tools-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 15px 0;
}

.modal-body h2 {
    color: var(--orange);
    font-family: var(--ff-oswald);
    font-size: 2rem;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.modal-body h3 {
    color: var(--white);
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    margin: 25px 0 15px 0;
    text-transform: uppercase;
    border-bottom: 2px solid var(--orange);
    padding-bottom: 5px;
}

.modal-body h4 {
    color: var(--light-gray);
    font-family: var(--ff-oswald);
    font-size: 1.1rem;
    margin: 20px 0 10px 0;
    text-transform: uppercase;
}

.modal-body ol {
    color: var(--light-gray);
    line-height: 1.8;
    padding-left: 20px;
}

.modal-body ol li {
    margin-bottom: 8px;
}

.modal-body p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Syntax Highlighting for Code Blocks */
.code-content {
    background: #1e1e1e;
    color: #d4d4d4;
}

.code-content .comment {
    color: #6a9955;
    font-style: italic;
}

.code-content .string {
    color: #ce9178;
}

.code-content .keyword {
    color: #569cd6;
}

.code-content .function {
    color: #dcdcaa;
}

.code-content .number {
    color: #b5cea8;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--raisin-black-3);
    border-radius: 50%;
    border-top-color: var(--orange);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    padding: 15px 20px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
}

.message.success {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.message.error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid #F44336;
    color: #F44336;
}

.message.info {
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid var(--orange);
    color: var(--orange);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: var(--raisin-black-1);
    color: var(--white);
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.9rem;
    border: 1px solid var(--orange);
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--raisin-black-3);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--orange), #e67e22);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Badge System */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.new {
    background: #4CAF50;
    color: white;
}

.badge.popular {
    background: var(--orange);
    color: white;
}

.badge.hard {
    background: #F44336;
    color: white;
}

/* Animated Background */
.animated-bg {
    position: relative;
    overflow: hidden;
}

.animated-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 165, 0, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Dark Mode Toggle */
.theme-toggle {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--orange);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 100;
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* Scroll to Top Button */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--orange);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 100;
}

.scroll-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* No Challenges Message */
.no-challenges {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--light-gray);
}

.no-challenges ion-icon {
    font-size: 4rem;
    color: var(--orange);
    margin-bottom: 20px;
}

.no-challenges h3 {
    color: var(--white);
    margin-bottom: 10px;
    font-family: var(--ff-oswald);
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: 40px;
}

/* Learning Resources Section */
.learning-resources {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.resource-card {
    background: var(--raisin-black-2);
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.resource-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.resource-icon {
    width: 80px;
    height: 80px;
    background: var(--orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.resource-icon ion-icon {
    font-size: 2.5rem;
    color: var(--white);
}

.resource-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.resource-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.resource-features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.resource-features li {
    color: var(--light-gray);
    padding: 5px 0;
    position: relative;
    padding-left: 20px;
}

.resource-features li::before {
    content: '✓';
    color: var(--orange);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.resource-link {
    display: inline-block;
    background: var(--orange);
    color: var(--white);
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.resource-link:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

/* Advanced Techniques Section */
.advanced-techniques {
    padding: 100px 0;
    background: var(--raisin-black-2);
}

.techniques-accordion {
    margin-top: 50px;
}

.technique-item {
    background: var(--raisin-black-1);
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.technique-item:hover {
    border-color: var(--orange);
}

.technique-header {
    padding: 25px 30px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--raisin-black-2);
    transition: background 0.3s ease;
}

.technique-header:hover {
    background: var(--raisin-black-3);
}

.technique-header h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin: 0;
    text-transform: uppercase;
}

.technique-header ion-icon {
    font-size: 1.5rem;
    color: var(--orange);
    transition: transform 0.3s ease;
}

.technique-content {
    display: none;
    padding: 30px;
}

.technique-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.technique-card {
    background: var(--raisin-black-2);
    padding: 25px;
    border-radius: 8px;
    border-left: 4px solid var(--orange);
}

.technique-card h4 {
    color: var(--white);
    font-family: var(--ff-oswald);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.technique-card p {
    color: var(--light-gray);
    line-height: 1.5;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.technique-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: var(--raisin-black-3);
    color: var(--orange);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(255, 165, 0, 0.3);
}

/* Newsletter Section */
.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--raisin-black-1) 0%, var(--raisin-black-2) 100%);
    text-align: center;
}

.newsletter-content h2 {
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.newsletter-content p {
    color: var(--light-gray);
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto 40px;
    gap: 15px;
}

.newsletter-form input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid var(--raisin-black-3);
    border-radius: 25px;
    background: var(--raisin-black-2);
    color: var(--white);
    font-size: 1rem;
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--orange);
}

.newsletter-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.newsletter-features .feature {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--light-gray);
}

.newsletter-features ion-icon {
    color: var(--orange);
    font-size: 1.2rem;
}
