/* Blog-specific styles */

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(135deg, var(--raisin-black-1) 0%, var(--raisin-black-2) 100%);
    padding: 150px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.blog-hero-content {
    position: relative;
    z-index: 2;
}

.blog-hero-title {
    font-family: var(--ff-o<PERSON><PERSON>);
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--white);
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.blog-hero-subtitle {
    font-size: clamp(1rem, 2vw, 1.2rem);
    color: var(--light-gray);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.blog-hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--ff-oswald);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--orange);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--light-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* CTF Categories */
.ctf-categories {
    padding: 100px 0;
    background: var(--raisin-black-2);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.category-card {
    background: var(--raisin-black-1);
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 165, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: var(--orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.category-icon ion-icon {
    font-size: 2.5rem;
    color: var(--white);
}

.category-card:hover .category-icon {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.5);
}

.category-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: 15px;
    text-transform: uppercase;
}

.category-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.challenge-count {
    display: inline-block;
    background: var(--orange);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Methodology Section */
.methodology-section {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

.methodology-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.step-card {
    background: var(--raisin-black-2);
    padding: 40px 30px;
    border-radius: 12px;
    position: relative;
    border-left: 4px solid var(--orange);
    transition: all 0.3s ease;
}

.step-card:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.step-number {
    position: absolute;
    top: -15px;
    left: 30px;
    background: var(--orange);
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--ff-oswald);
    font-weight: 700;
    font-size: 1.2rem;
}

.step-card h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 15px;
    margin-top: 10px;
    text-transform: uppercase;
}

.step-card p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.step-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-tag {
    background: var(--raisin-black-3);
    color: var(--orange);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--orange);
}

/* Tools Section */
.tools-section {
    padding: 100px 0;
    background: var(--raisin-black-2);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.tool-category {
    background: var(--raisin-black-1);
    padding: 40px 30px;
    border-radius: 12px;
    border-top: 4px solid var(--orange);
}

.tool-category h3 {
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    color: var(--white);
    margin-bottom: 25px;
    text-transform: uppercase;
    text-align: center;
}

.tool-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tool-item {
    background: var(--raisin-black-2);
    padding: 20px;
    border-radius: 8px;
    border-left: 3px solid var(--orange);
    transition: all 0.3s ease;
}

.tool-item:hover {
    background: var(--raisin-black-3);
    transform: translateX(5px);
}

.tool-name {
    display: block;
    font-weight: 600;
    color: var(--orange);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.tool-desc {
    color: var(--light-gray);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Challenges Section */
.challenges-section {
    padding: 100px 0;
    background: var(--raisin-black-1);
}

.challenge-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin: 40px 0;
}

.filter-btn {
    background: var(--raisin-black-2);
    color: var(--light-gray);
    border: 2px solid var(--raisin-black-3);
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--orange);
    color: var(--white);
    border-color: var(--orange);
    transform: translateY(-2px);
}

.search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto 40px;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    background: var(--raisin-black-2);
    border: 2px solid var(--raisin-black-3);
    border-radius: 25px;
    color: var(--white);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--orange);
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);
}

.search-input::placeholder {
    color: var(--light-gray);
}

.search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--orange);
    font-size: 1.2rem;
}

.challenges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.challenge-card {
    background: var(--raisin-black-2);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.challenge-card:hover {
    border-color: var(--orange);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 165, 0, 0.2);
}

.challenge-header {
    padding: 25px;
    border-bottom: 1px solid var(--raisin-black-3);
}

.challenge-title {
    font-family: var(--ff-oswald);
    font-size: 1.3rem;
    color: var(--white);
    margin-bottom: 10px;
    text-transform: uppercase;
}

.challenge-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.challenge-category {
    background: var(--orange);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.challenge-difficulty {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy { background: #4CAF50; color: white; }
.difficulty-medium { background: #FF9800; color: white; }
.difficulty-hard { background: #F44336; color: white; }

.challenge-description {
    color: var(--light-gray);
    line-height: 1.5;
    font-size: 0.9rem;
}

.challenge-footer {
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.challenge-points {
    font-weight: 600;
    color: var(--orange);
    font-size: 1.1rem;
}

.challenge-solves {
    color: var(--light-gray);
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--raisin-black-1);
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    border: 2px solid var(--orange);
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 25px;
    color: var(--light-gray);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--orange);
}

.modal-body {
    padding: 40px;
}

/* Code Block Styles */
.code-block {
    background: var(--raisin-black-3);
    border: 1px solid var(--raisin-black-2);
    border-radius: 8px;
    margin: 20px 0;
    position: relative;
}

.code-header {
    background: var(--raisin-black-2);
    padding: 10px 15px;
    border-bottom: 1px solid var(--raisin-black-1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-language {
    color: var(--orange);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.copy-btn {
    background: var(--orange);
    color: var(--white);
    border: none;
    padding: 5px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #e67e22;
    transform: scale(1.05);
}

.code-content {
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--light-gray);
    overflow-x: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-hero {
        padding: 120px 0 80px;
    }
    
    .blog-hero-stats {
        gap: 20px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .categories-grid,
    .methodology-steps,
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .challenge-filters {
        gap: 10px;
    }
    
    .filter-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .challenges-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .blog-hero-title {
        font-size: 2rem;
    }

    .blog-hero-subtitle {
        font-size: 0.9rem;
    }

    .category-card,
    .step-card,
    .tool-category {
        padding: 25px 20px;
    }

    .challenge-header,
    .challenge-footer {
        padding: 20px;
    }
}

/* Advanced Features */
.challenge-info {
    display: flex;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.challenge-info .challenge-category,
.challenge-info .challenge-difficulty,
.challenge-info .challenge-points {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.challenge-info .challenge-points {
    background: var(--orange);
    color: var(--white);
}

.tools-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 15px 0;
}

.modal-body h2 {
    color: var(--orange);
    font-family: var(--ff-oswald);
    font-size: 2rem;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.modal-body h3 {
    color: var(--white);
    font-family: var(--ff-oswald);
    font-size: 1.4rem;
    margin: 25px 0 15px 0;
    text-transform: uppercase;
    border-bottom: 2px solid var(--orange);
    padding-bottom: 5px;
}

.modal-body h4 {
    color: var(--light-gray);
    font-family: var(--ff-oswald);
    font-size: 1.1rem;
    margin: 20px 0 10px 0;
    text-transform: uppercase;
}

.modal-body ol {
    color: var(--light-gray);
    line-height: 1.8;
    padding-left: 20px;
}

.modal-body ol li {
    margin-bottom: 8px;
}

.modal-body p {
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Syntax Highlighting for Code Blocks */
.code-content {
    background: #1e1e1e;
    color: #d4d4d4;
}

.code-content .comment {
    color: #6a9955;
    font-style: italic;
}

.code-content .string {
    color: #ce9178;
}

.code-content .keyword {
    color: #569cd6;
}

.code-content .function {
    color: #dcdcaa;
}

.code-content .number {
    color: #b5cea8;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--raisin-black-3);
    border-radius: 50%;
    border-top-color: var(--orange);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    padding: 15px 20px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
}

.message.success {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.message.error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid #F44336;
    color: #F44336;
}

.message.info {
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid var(--orange);
    color: var(--orange);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: var(--raisin-black-1);
    color: var(--white);
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.9rem;
    border: 1px solid var(--orange);
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--raisin-black-3);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--orange), #e67e22);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Badge System */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.new {
    background: #4CAF50;
    color: white;
}

.badge.popular {
    background: var(--orange);
    color: white;
}

.badge.hard {
    background: #F44336;
    color: white;
}

/* Animated Background */
.animated-bg {
    position: relative;
    overflow: hidden;
}

.animated-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 165, 0, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Dark Mode Toggle */
.theme-toggle {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--orange);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 100;
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

/* Scroll to Top Button */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--orange);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 100;
}

.scroll-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}
