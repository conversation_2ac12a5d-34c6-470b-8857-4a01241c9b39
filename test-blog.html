<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTF Challenge Browser Test</title>
    <link rel="stylesheet" href="blog.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-status {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .status-ok { color: #4CAF50; }
        .status-error { color: #F44336; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🐺 CTF Challenge Browser Test</h1>
            <p>Testing all functions and features</p>
        </div>

        <div class="test-status" id="testStatus">
            <h3>System Status</h3>
            <div class="status-item">
                <span>JavaScript Loading:</span>
                <span id="jsStatus" class="status-error">❌ Not Loaded</span>
            </div>
            <div class="status-item">
                <span>Challenges Data:</span>
                <span id="dataStatus" class="status-error">❌ Not Loaded</span>
            </div>
            <div class="status-item">
                <span>DOM Elements:</span>
                <span id="domStatus" class="status-error">❌ Not Ready</span>
            </div>
            <div class="status-item">
                <span>Event Listeners:</span>
                <span id="eventStatus" class="status-error">❌ Not Setup</span>
            </div>
        </div>

        <!-- Challenge Statistics -->
        <div class="challenge-stats">
            <div class="stat-card">
                <span class="stat-value" id="totalChallenges">0</span>
                <span class="stat-label">Total Challenges</span>
            </div>
            <div class="stat-card">
                <span class="stat-value" id="totalCategories">0</span>
                <span class="stat-label">Categories</span>
            </div>
            <div class="stat-card">
                <span class="stat-value">3</span>
                <span class="stat-label">Difficulty Levels</span>
            </div>
            <div class="stat-card">
                <span class="stat-value">200+</span>
                <span class="stat-label">Solving Methods</span>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="challenge-filters">
            <button class="filter-btn active" data-filter="all">All Categories</button>
            <button class="filter-btn" data-filter="web">Web</button>
            <button class="filter-btn" data-filter="crypto">Crypto</button>
            <button class="filter-btn" data-filter="forensics">Forensics</button>
            <button class="filter-btn" data-filter="pwn">Pwn</button>
            <button class="filter-btn" data-filter="reverse">Reverse</button>
            <button class="filter-btn" data-filter="misc">Misc</button>
            <button class="filter-btn" data-filter="mobile">Mobile</button>
            <button class="filter-btn" data-filter="cloud">Cloud</button>
        </div>

        <!-- Difficulty Filter -->
        <div class="difficulty-filters">
            <button class="diff-filter-btn active" data-difficulty="all">All Levels</button>
            <button class="diff-filter-btn" data-difficulty="easy">Easy</button>
            <button class="diff-filter-btn" data-difficulty="medium">Medium</button>
            <button class="diff-filter-btn" data-difficulty="hard">Hard</button>
        </div>

        <!-- Search Bar -->
        <div class="search-container">
            <input type="text" id="challengeSearch" placeholder="Search challenges, tools, or techniques..." class="search-input">
            <ion-icon name="search-outline" class="search-icon"></ion-icon>
            <div class="search-suggestions" id="searchSuggestions"></div>
        </div>

        <!-- Test Buttons -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn btn-primary" onclick="testRandomChallenge()">🎲 Random Challenge</button>
            <button class="btn btn-secondary" onclick="testBookmarks()">⭐ Test Bookmarks</button>
            <button class="btn btn-primary" onclick="testProgress()">📊 Test Progress</button>
            <button class="btn btn-secondary" onclick="runAllTests()">🧪 Run All Tests</button>
        </div>

        <!-- Challenge Grid -->
        <div class="challenges-grid" id="challengesGrid">
            <!-- Challenges will be populated here -->
        </div>

        <!-- Load More Button -->
        <div class="load-more-container">
            <button class="btn btn-secondary" id="loadMoreBtn">Load More Challenges</button>
        </div>
    </div>

    <!-- Challenge Modal -->
    <div class="modal" id="challengeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Challenge Details</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Challenge details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Test Functions -->
    <script>
        function testRandomChallenge() {
            if (typeof randomChallenge === 'function') {
                randomChallenge();
            } else {
                alert('❌ randomChallenge function not found');
            }
        }

        function testBookmarks() {
            if (typeof showBookmarkedChallenges === 'function') {
                showBookmarkedChallenges();
            } else {
                alert('❌ showBookmarkedChallenges function not found');
            }
        }

        function testProgress() {
            if (typeof openModal === 'function') {
                openModal('progressModal');
            } else {
                alert('❌ openModal function not found');
            }
        }

        function runAllTests() {
            const tests = [
                { name: 'allChallenges array', test: () => typeof allChallenges !== 'undefined' && allChallenges.length > 0 },
                { name: 'DOM elements', test: () => document.getElementById('challengesGrid') !== null },
                { name: 'Filter functions', test: () => typeof applyFilters === 'function' },
                { name: 'Render functions', test: () => typeof renderChallengesPage === 'function' },
                { name: 'Modal functions', test: () => typeof openChallengeModal === 'function' },
                { name: 'Toast system', test: () => typeof showToast === 'function' }
            ];

            let passed = 0;
            let results = '🧪 Test Results:\n\n';

            tests.forEach(test => {
                try {
                    const result = test.test();
                    if (result) {
                        results += `✅ ${test.name}: PASSED\n`;
                        passed++;
                    } else {
                        results += `❌ ${test.name}: FAILED\n`;
                    }
                } catch (error) {
                    results += `❌ ${test.name}: ERROR - ${error.message}\n`;
                }
            });

            results += `\n📊 Results: ${passed}/${tests.length} tests passed`;
            alert(results);
        }

        // Update status when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                // Check JavaScript loading
                if (typeof allChallenges !== 'undefined') {
                    document.getElementById('jsStatus').innerHTML = '✅ Loaded';
                    document.getElementById('jsStatus').className = 'status-ok';
                }

                // Check data loading
                if (typeof allChallenges !== 'undefined' && allChallenges.length > 0) {
                    document.getElementById('dataStatus').innerHTML = `✅ ${allChallenges.length} challenges`;
                    document.getElementById('dataStatus').className = 'status-ok';
                    
                    document.getElementById('totalChallenges').textContent = allChallenges.length;
                    const categories = [...new Set(allChallenges.map(c => c.category))];
                    document.getElementById('totalCategories').textContent = categories.length;
                }

                // Check DOM elements
                const challengesGrid = document.getElementById('challengesGrid');
                if (challengesGrid) {
                    document.getElementById('domStatus').innerHTML = '✅ Ready';
                    document.getElementById('domStatus').className = 'status-ok';
                }

                // Check event listeners
                const filterBtns = document.querySelectorAll('.filter-btn');
                if (filterBtns.length > 0) {
                    document.getElementById('eventStatus').innerHTML = '✅ Active';
                    document.getElementById('eventStatus').className = 'status-ok';
                }
            }, 1000);
        });
    </script>

    <!-- Ion Icons -->
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>
    
    <!-- Main Blog Script -->
    <script src="blog.js"></script>
</body>
</html>
