<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="description" content="Cyber Wolf CTF Blog - Comprehensive CTF challenge solving methodologies, tools, and techniques">
    <meta name="keywords" content="CTF, capture the flag, cybersecurity, hacking, penetration testing, cyber wolf">
    <meta name="author" content="Cyber Wolf">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Cyber Wolf CTF Blog - Challenge Solving Methodologies">
    <meta property="og:description" content="Master CTF challenges with our comprehensive solving methodologies and tools">
    <meta property="og:type" content="website">
    
    <title>Cyber Wolf CTF Blog - Challenge Solving Methodologies</title>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Oswald:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="blog.css">
    <link rel="icon" href="assets_I/logo/logo.jpeg">
</head>
<body id="top">
    <!-- Header -->
    <header class="header">
        <div class="overlay" data-overlay></div>
        <div class="container">
            <a href="index.html" class="logo">
                <div class="logo-container">
                    <img src="assets_I/logo/logo.jpeg" alt="Cyber Wolf Logo" class="logo-image">
                    <span class="logo-text">Cyber Wolf</span>
                </div>
            </a>

            <button class="nav-open-btn" data-nav-open-btn>
                <ion-icon name="menu-outline"></ion-icon>
            </button>

            <nav class="navbar" data-navbar>
                <div class="navbar-top">
                    <a href="index.html" class="logo">
                        <img src="assets_I/logo/logo.jpeg" alt="Cyber Wolf Logo">
                    </a>
                    <button class="nav-close-btn" data-nav-close-btn>
                        <ion-icon name="close-outline"></ion-icon>
                    </button>
                </div>

                <ul class="navbar-list">
                    <li><a href="index.html" class="navbar-link">Home</a></li>
                    <li><a href="#ctf-methodology" class="navbar-link">CTF Methodology</a></li>
                    <li><a href="#challenges" class="navbar-link">Challenges</a></li>
                    <li><a href="#tools" class="navbar-link">Tools</a></li>
                    <li><a href="#writeups" class="navbar-link">Write-ups</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="blog-hero">
        <div class="container">
            <div class="blog-hero-content">
                <h1 class="blog-hero-title">CTF Challenge Solving Hub</h1>
                <p class="blog-hero-subtitle">Master Capture The Flag competitions with comprehensive methodologies, tools, and 200+ solving techniques. From beginner to advanced level challenges.</p>
                <div class="blog-hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">75+</span>
                        <span class="stat-label">CTF Challenges</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">200+</span>
                        <span class="stat-label">Solving Methods</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Tools Covered</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Categories</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="#challenges" class="btn btn-primary">Start Learning</a>
                    <a href="#methodology" class="btn btn-secondary">View Methodology</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTF Categories -->
    <section class="ctf-categories" id="ctf-methodology">
        <div class="container">
            <h2 class="section-title">CTF Challenge Categories</h2>
            <p class="section-subtitle">Explore comprehensive challenge categories with detailed solving methodologies</p>
            <div class="categories-grid">
                <div class="category-card" data-category="web">
                    <div class="category-icon">
                        <ion-icon name="globe-outline"></ion-icon>
                    </div>
                    <h3>Web Exploitation</h3>
                    <p>SQL Injection, XSS, CSRF, Authentication Bypass, SSRF, XXE, Deserialization</p>
                    <span class="challenge-count">20 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">8 Easy</span>
                        <span class="diff-medium">8 Medium</span>
                        <span class="diff-hard">4 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="crypto">
                    <div class="category-icon">
                        <ion-icon name="lock-closed-outline"></ion-icon>
                    </div>
                    <h3>Cryptography</h3>
                    <p>Classical Ciphers, RSA, AES, Hash Functions, ECC, Blockchain</p>
                    <span class="challenge-count">15 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">5 Easy</span>
                        <span class="diff-medium">6 Medium</span>
                        <span class="diff-hard">4 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="forensics">
                    <div class="category-icon">
                        <ion-icon name="search-outline"></ion-icon>
                    </div>
                    <h3>Digital Forensics</h3>
                    <p>Memory Analysis, Network Forensics, File Recovery, Mobile Forensics</p>
                    <span class="challenge-count">12 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">4 Easy</span>
                        <span class="diff-medium">5 Medium</span>
                        <span class="diff-hard">3 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="pwn">
                    <div class="category-icon">
                        <ion-icon name="bug-outline"></ion-icon>
                    </div>
                    <h3>Binary Exploitation</h3>
                    <p>Buffer Overflow, ROP, Format String, Heap Exploitation, Kernel Pwn</p>
                    <span class="challenge-count">10 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">2 Easy</span>
                        <span class="diff-medium">4 Medium</span>
                        <span class="diff-hard">4 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="reverse">
                    <div class="category-icon">
                        <ion-icon name="code-slash-outline"></ion-icon>
                    </div>
                    <h3>Reverse Engineering</h3>
                    <p>Assembly Analysis, Malware Analysis, Decompilation, Obfuscation</p>
                    <span class="challenge-count">8 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">2 Easy</span>
                        <span class="diff-medium">3 Medium</span>
                        <span class="diff-hard">3 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="misc">
                    <div class="category-icon">
                        <ion-icon name="extension-puzzle-outline"></ion-icon>
                    </div>
                    <h3>Miscellaneous</h3>
                    <p>Steganography, OSINT, Programming, Blockchain, AI/ML</p>
                    <span class="challenge-count">10 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">4 Easy</span>
                        <span class="diff-medium">4 Medium</span>
                        <span class="diff-hard">2 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="mobile">
                    <div class="category-icon">
                        <ion-icon name="phone-portrait-outline"></ion-icon>
                    </div>
                    <h3>Mobile Security</h3>
                    <p>Android APK, iOS IPA, Mobile Forensics, App Security</p>
                    <span class="challenge-count">6 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">2 Easy</span>
                        <span class="diff-medium">2 Medium</span>
                        <span class="diff-hard">2 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="cloud">
                    <div class="category-icon">
                        <ion-icon name="cloud-outline"></ion-icon>
                    </div>
                    <h3>Cloud Security</h3>
                    <p>AWS, Azure, GCP, Container Security, Serverless</p>
                    <span class="challenge-count">5 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">1 Easy</span>
                        <span class="diff-medium">2 Medium</span>
                        <span class="diff-hard">2 Hard</span>
                    </div>
                </div>

                <div class="category-card" data-category="hardware">
                    <div class="category-icon">
                        <ion-icon name="hardware-chip-outline"></ion-icon>
                    </div>
                    <h3>Hardware Hacking</h3>
                    <p>IoT Security, Firmware Analysis, Side-Channel Attacks</p>
                    <span class="challenge-count">4 Challenges</span>
                    <div class="difficulty-breakdown">
                        <span class="diff-easy">1 Easy</span>
                        <span class="diff-medium">1 Medium</span>
                        <span class="diff-hard">2 Hard</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Methodology Section -->
    <section class="methodology-section" id="methodology">
        <div class="container">
            <h2 class="section-title">CTF Solving Methodology</h2>
            <p class="section-subtitle">Follow our proven 6-step methodology for systematic CTF challenge solving</p>

            <div class="methodology-overview">
                <div class="methodology-flow">
                    <div class="flow-item">Recon</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">Analysis</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">Research</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">Exploit</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">Verify</div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">Document</div>
                </div>
            </div>

            <div class="methodology-steps">
                <div class="step-card">
                    <div class="step-number">01</div>
                    <h3>Reconnaissance</h3>
                    <p>Gather comprehensive information about the challenge, analyze file types, metadata, and identify potential attack vectors through systematic enumeration.</p>
                    <div class="step-details">
                        <h4>Key Activities:</h4>
                        <ul>
                            <li>File type identification and metadata analysis</li>
                            <li>String extraction and pattern recognition</li>
                            <li>Network service enumeration</li>
                            <li>Source code review (if available)</li>
                        </ul>
                    </div>
                    <div class="step-tools">
                        <span class="tool-tag">file</span>
                        <span class="tool-tag">strings</span>
                        <span class="tool-tag">binwalk</span>
                        <span class="tool-tag">exiftool</span>
                        <span class="tool-tag">nmap</span>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">02</div>
                    <h3>Analysis</h3>
                    <p>Deep dive into the challenge using category-specific tools and techniques. Understand the underlying technology and potential vulnerabilities.</p>
                    <div class="step-details">
                        <h4>Key Activities:</h4>
                        <ul>
                            <li>Static and dynamic analysis</li>
                            <li>Code review and vulnerability assessment</li>
                            <li>Protocol analysis and traffic inspection</li>
                            <li>Cryptographic algorithm identification</li>
                        </ul>
                    </div>
                    <div class="step-tools">
                        <span class="tool-tag">ghidra</span>
                        <span class="tool-tag">wireshark</span>
                        <span class="tool-tag">burp suite</span>
                        <span class="tool-tag">ida pro</span>
                        <span class="tool-tag">volatility</span>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">03</div>
                    <h3>Research</h3>
                    <p>Research known vulnerabilities, exploits, and techniques. Leverage public databases, CVEs, and community knowledge.</p>
                    <div class="step-details">
                        <h4>Key Activities:</h4>
                        <ul>
                            <li>CVE database searches</li>
                            <li>Exploit database research</li>
                            <li>Academic paper review</li>
                            <li>Community forum consultation</li>
                        </ul>
                    </div>
                    <div class="step-tools">
                        <span class="tool-tag">exploit-db</span>
                        <span class="tool-tag">cve.mitre.org</span>
                        <span class="tool-tag">google dorking</span>
                        <span class="tool-tag">github</span>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">04</div>
                    <h3>Exploitation</h3>
                    <p>Execute the attack based on discovered vulnerabilities. Develop custom exploits or adapt existing ones to extract the flag.</p>
                    <div class="step-details">
                        <h4>Key Activities:</h4>
                        <ul>
                            <li>Exploit development and testing</li>
                            <li>Payload crafting and delivery</li>
                            <li>Privilege escalation techniques</li>
                            <li>Flag extraction and validation</li>
                        </ul>
                    </div>
                    <div class="step-tools">
                        <span class="tool-tag">python</span>
                        <span class="tool-tag">metasploit</span>
                        <span class="tool-tag">pwntools</span>
                        <span class="tool-tag">custom scripts</span>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">05</div>
                    <h3>Verification</h3>
                    <p>Verify the exploit works consistently and the flag is correct. Test edge cases and ensure reproducibility.</p>
                    <div class="step-details">
                        <h4>Key Activities:</h4>
                        <ul>
                            <li>Flag format validation</li>
                            <li>Exploit reliability testing</li>
                            <li>Alternative solution exploration</li>
                            <li>Impact assessment</li>
                        </ul>
                    </div>
                    <div class="step-tools">
                        <span class="tool-tag">testing frameworks</span>
                        <span class="tool-tag">automation scripts</span>
                        <span class="tool-tag">validation tools</span>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number">06</div>
                    <h3>Documentation</h3>
                    <p>Create comprehensive write-ups documenting the solution process, tools used, and lessons learned for future reference.</p>
                    <div class="step-details">
                        <h4>Key Activities:</h4>
                        <ul>
                            <li>Step-by-step write-up creation</li>
                            <li>Screenshot and evidence collection</li>
                            <li>Code snippet documentation</li>
                            <li>Knowledge sharing with community</li>
                        </ul>
                    </div>
                    <div class="step-tools">
                        <span class="tool-tag">markdown</span>
                        <span class="tool-tag">screenshots</span>
                        <span class="tool-tag">code snippets</span>
                        <span class="tool-tag">git</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tools Section -->
    <section class="tools-section" id="tools">
        <div class="container">
            <h2 class="section-title">Essential CTF Tools Arsenal</h2>
            <p class="section-subtitle">Comprehensive toolkit covering all CTF categories with detailed usage guides</p>

            <div class="tools-filter">
                <button class="tool-filter-btn active" data-tool-category="all">All Tools</button>
                <button class="tool-filter-btn" data-tool-category="web">Web</button>
                <button class="tool-filter-btn" data-tool-category="crypto">Crypto</button>
                <button class="tool-filter-btn" data-tool-category="forensics">Forensics</button>
                <button class="tool-filter-btn" data-tool-category="pwn">Binary</button>
                <button class="tool-filter-btn" data-tool-category="reverse">Reverse</button>
                <button class="tool-filter-btn" data-tool-category="misc">Misc</button>
            </div>

            <div class="tools-grid">
                <div class="tool-category" data-category="web">
                    <h3>Web Exploitation</h3>
                    <div class="tool-list">
                        <div class="tool-item">
                            <span class="tool-name">Burp Suite Professional</span>
                            <span class="tool-desc">Advanced web application security testing platform</span>
                            <div class="tool-commands">
                                <code>burpsuite --config-file=project.json</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">OWASP ZAP</span>
                            <span class="tool-desc">Free web application security scanner</span>
                            <div class="tool-commands">
                                <code>zap.sh -cmd -quickurl http://target.com</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">SQLMap</span>
                            <span class="tool-desc">Automatic SQL injection exploitation tool</span>
                            <div class="tool-commands">
                                <code>sqlmap -u "http://target.com/page?id=1" --dbs</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Nikto</span>
                            <span class="tool-desc">Web server vulnerability scanner</span>
                            <div class="tool-commands">
                                <code>nikto -h http://target.com</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Gobuster</span>
                            <span class="tool-desc">Directory and file brute-forcer</span>
                            <div class="tool-commands">
                                <code>gobuster dir -u http://target.com -w wordlist.txt</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Wfuzz</span>
                            <span class="tool-desc">Web application fuzzer</span>
                            <div class="tool-commands">
                                <code>wfuzz -c -z file,wordlist.txt http://target.com/FUZZ</code>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-category" data-category="crypto">
                    <h3>Cryptography</h3>
                    <div class="tool-list">
                        <div class="tool-item">
                            <span class="tool-name">CyberChef</span>
                            <span class="tool-desc">Swiss army knife for data transformation</span>
                            <div class="tool-commands">
                                <code>Online tool: gchq.github.io/CyberChef</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">John the Ripper</span>
                            <span class="tool-desc">Password cracking tool</span>
                            <div class="tool-commands">
                                <code>john --wordlist=rockyou.txt hashes.txt</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Hashcat</span>
                            <span class="tool-desc">Advanced password recovery utility</span>
                            <div class="tool-commands">
                                <code>hashcat -m 0 -a 0 hashes.txt rockyou.txt</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">RsaCtfTool</span>
                            <span class="tool-desc">RSA attack tool for CTF</span>
                            <div class="tool-commands">
                                <code>python3 RsaCtfTool.py --publickey key.pub --uncipherfile flag.enc</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">FeatherDuster</span>
                            <span class="tool-desc">Cryptanalysis tool for breaking crypto</span>
                            <div class="tool-commands">
                                <code>python featherduster.py</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Sage Math</span>
                            <span class="tool-desc">Mathematical software for cryptography</span>
                            <div class="tool-commands">
                                <code>sage: factor(n)</code>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-category" data-category="forensics">
                    <h3>Digital Forensics</h3>
                    <div class="tool-list">
                        <div class="tool-item">
                            <span class="tool-name">Volatility 3</span>
                            <span class="tool-desc">Advanced memory forensics framework</span>
                            <div class="tool-commands">
                                <code>vol.py -f memory.dmp windows.pslist</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Wireshark</span>
                            <span class="tool-desc">Network protocol analyzer</span>
                            <div class="tool-commands">
                                <code>tshark -r capture.pcap -Y "http.request.method==POST"</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Autopsy</span>
                            <span class="tool-desc">Digital forensics platform</span>
                            <div class="tool-commands">
                                <code>autopsy</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Binwalk</span>
                            <span class="tool-desc">Firmware analysis tool</span>
                            <div class="tool-commands">
                                <code>binwalk -e firmware.bin</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Foremost</span>
                            <span class="tool-desc">File carving tool</span>
                            <div class="tool-commands">
                                <code>foremost -i disk.img -o output/</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Steghide</span>
                            <span class="tool-desc">Steganography tool</span>
                            <div class="tool-commands">
                                <code>steghide extract -sf image.jpg</code>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-category" data-category="pwn">
                    <h3>Binary Exploitation</h3>
                    <div class="tool-list">
                        <div class="tool-item">
                            <span class="tool-name">GDB with GEF</span>
                            <span class="tool-desc">Enhanced GNU debugger</span>
                            <div class="tool-commands">
                                <code>gdb ./binary -ex "run"</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Pwntools</span>
                            <span class="tool-desc">CTF framework and exploit development library</span>
                            <div class="tool-commands">
                                <code>from pwn import *; p = process('./binary')</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">ROPgadget</span>
                            <span class="tool-desc">ROP gadget finder and auto-roper</span>
                            <div class="tool-commands">
                                <code>ROPgadget --binary ./binary --ropchain</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Checksec</span>
                            <span class="tool-desc">Binary security property checker</span>
                            <div class="tool-commands">
                                <code>checksec --file=./binary</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">One_gadget</span>
                            <span class="tool-desc">Magic gadget finder for libc</span>
                            <div class="tool-commands">
                                <code>one_gadget /lib/x86_64-linux-gnu/libc.so.6</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Radare2</span>
                            <span class="tool-desc">Reverse engineering framework</span>
                            <div class="tool-commands">
                                <code>r2 -A ./binary</code>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-category" data-category="reverse">
                    <h3>Reverse Engineering</h3>
                    <div class="tool-list">
                        <div class="tool-item">
                            <span class="tool-name">Ghidra</span>
                            <span class="tool-desc">NSA's reverse engineering suite</span>
                            <div class="tool-commands">
                                <code>ghidraRun</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">IDA Pro</span>
                            <span class="tool-desc">Industry standard disassembler</span>
                            <div class="tool-commands">
                                <code>ida64.exe binary.exe</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">x64dbg</span>
                            <span class="tool-desc">Windows debugger</span>
                            <div class="tool-commands">
                                <code>x64dbg.exe binary.exe</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">JADX</span>
                            <span class="tool-desc">Android APK decompiler</span>
                            <div class="tool-commands">
                                <code>jadx -d output app.apk</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">APKTool</span>
                            <span class="tool-desc">Android APK reverse engineering tool</span>
                            <div class="tool-commands">
                                <code>apktool d app.apk</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Frida</span>
                            <span class="tool-desc">Dynamic instrumentation toolkit</span>
                            <div class="tool-commands">
                                <code>frida -U -f com.app.name -l script.js</code>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-category" data-category="misc">
                    <h3>Miscellaneous Tools</h3>
                    <div class="tool-list">
                        <div class="tool-item">
                            <span class="tool-name">Nmap</span>
                            <span class="tool-desc">Network discovery and security auditing</span>
                            <div class="tool-commands">
                                <code>nmap -sC -sV -oA scan target.com</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Metasploit</span>
                            <span class="tool-desc">Penetration testing framework</span>
                            <div class="tool-commands">
                                <code>msfconsole</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">TheHarvester</span>
                            <span class="tool-desc">OSINT information gathering</span>
                            <div class="tool-commands">
                                <code>theHarvester -d target.com -b google</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Shodan CLI</span>
                            <span class="tool-desc">Internet-connected device search</span>
                            <div class="tool-commands">
                                <code>shodan search "apache 2.4.1"</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">ExifTool</span>
                            <span class="tool-desc">Metadata extraction tool</span>
                            <div class="tool-commands">
                                <code>exiftool image.jpg</code>
                            </div>
                        </div>
                        <div class="tool-item">
                            <span class="tool-name">Strings</span>
                            <span class="tool-desc">Extract printable strings from files</span>
                            <div class="tool-commands">
                                <code>strings -a binary | grep -i flag</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Learning Resources Section -->
    <section class="learning-resources">
        <div class="container">
            <h2 class="section-title">Advanced Learning Resources</h2>
            <p class="section-subtitle">Comprehensive resources to master CTF techniques and cybersecurity skills</p>

            <div class="resources-grid">
                <div class="resource-card">
                    <div class="resource-icon">
                        <ion-icon name="book-outline"></ion-icon>
                    </div>
                    <h3>CTF Writeups Database</h3>
                    <p>Access 500+ detailed writeups from major CTF competitions worldwide</p>
                    <ul class="resource-features">
                        <li>Step-by-step solutions</li>
                        <li>Multiple solution approaches</li>
                        <li>Tool usage examples</li>
                        <li>Lessons learned</li>
                    </ul>
                    <a href="#writeups" class="resource-link">Explore Writeups</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">
                        <ion-icon name="code-outline"></ion-icon>
                    </div>
                    <h3>Exploit Development Lab</h3>
                    <p>Hands-on environment for practicing exploit development techniques</p>
                    <ul class="resource-features">
                        <li>Buffer overflow tutorials</li>
                        <li>ROP chain building</li>
                        <li>Heap exploitation</li>
                        <li>Kernel exploitation</li>
                    </ul>
                    <a href="#lab" class="resource-link">Access Lab</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">
                        <ion-icon name="school-outline"></ion-icon>
                    </div>
                    <h3>Video Tutorials</h3>
                    <p>Comprehensive video series covering all CTF categories</p>
                    <ul class="resource-features">
                        <li>Beginner to advanced levels</li>
                        <li>Live solving sessions</li>
                        <li>Tool demonstrations</li>
                        <li>Expert interviews</li>
                    </ul>
                    <a href="#videos" class="resource-link">Watch Videos</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">
                        <ion-icon name="people-outline"></ion-icon>
                    </div>
                    <h3>Community Forum</h3>
                    <p>Connect with fellow CTF enthusiasts and security researchers</p>
                    <ul class="resource-features">
                        <li>Discussion boards</li>
                        <li>Team formation</li>
                        <li>Challenge hints</li>
                        <li>Career guidance</li>
                    </ul>
                    <a href="#community" class="resource-link">Join Community</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Advanced Techniques Section -->
    <section class="advanced-techniques">
        <div class="container">
            <h2 class="section-title">Advanced CTF Techniques</h2>
            <p class="section-subtitle">Master advanced techniques used in high-level CTF competitions</p>

            <div class="techniques-accordion">
                <div class="technique-item">
                    <div class="technique-header" onclick="toggleTechnique(this)">
                        <h3>Advanced Web Exploitation</h3>
                        <ion-icon name="chevron-down-outline"></ion-icon>
                    </div>
                    <div class="technique-content">
                        <div class="technique-grid">
                            <div class="technique-card">
                                <h4>Server-Side Template Injection (SSTI)</h4>
                                <p>Exploit template engines to achieve RCE</p>
                                <div class="technique-tags">
                                    <span class="tag">Jinja2</span>
                                    <span class="tag">Twig</span>
                                    <span class="tag">Smarty</span>
                                </div>
                            </div>
                            <div class="technique-card">
                                <h4>Deserialization Attacks</h4>
                                <p>Exploit unsafe deserialization in various languages</p>
                                <div class="technique-tags">
                                    <span class="tag">Java</span>
                                    <span class="tag">Python</span>
                                    <span class="tag">PHP</span>
                                </div>
                            </div>
                            <div class="technique-card">
                                <h4>XXE (XML External Entity)</h4>
                                <p>Exploit XML parsers for file disclosure and SSRF</p>
                                <div class="technique-tags">
                                    <span class="tag">File Read</span>
                                    <span class="tag">SSRF</span>
                                    <span class="tag">DoS</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="technique-item">
                    <div class="technique-header" onclick="toggleTechnique(this)">
                        <h3>Advanced Cryptography</h3>
                        <ion-icon name="chevron-down-outline"></ion-icon>
                    </div>
                    <div class="technique-content">
                        <div class="technique-grid">
                            <div class="technique-card">
                                <h4>Lattice-Based Attacks</h4>
                                <p>Use lattice reduction for cryptographic attacks</p>
                                <div class="technique-tags">
                                    <span class="tag">LLL</span>
                                    <span class="tag">CVP</span>
                                    <span class="tag">SVP</span>
                                </div>
                            </div>
                            <div class="technique-card">
                                <h4>Side-Channel Analysis</h4>
                                <p>Exploit timing and power consumption</p>
                                <div class="technique-tags">
                                    <span class="tag">Timing</span>
                                    <span class="tag">Power</span>
                                    <span class="tag">Cache</span>
                                </div>
                            </div>
                            <div class="technique-card">
                                <h4>Elliptic Curve Attacks</h4>
                                <p>Advanced ECC cryptanalysis techniques</p>
                                <div class="technique-tags">
                                    <span class="tag">Invalid Curve</span>
                                    <span class="tag">Twist Attack</span>
                                    <span class="tag">MOV Attack</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="technique-item">
                    <div class="technique-header" onclick="toggleTechnique(this)">
                        <h3>Advanced Binary Exploitation</h3>
                        <ion-icon name="chevron-down-outline"></ion-icon>
                    </div>
                    <div class="technique-content">
                        <div class="technique-grid">
                            <div class="technique-card">
                                <h4>Heap Exploitation</h4>
                                <p>Advanced heap manipulation techniques</p>
                                <div class="technique-tags">
                                    <span class="tag">Tcache</span>
                                    <span class="tag">Fastbin</span>
                                    <span class="tag">House of</span>
                                </div>
                            </div>
                            <div class="technique-card">
                                <h4>Kernel Exploitation</h4>
                                <p>Privilege escalation through kernel bugs</p>
                                <div class="technique-tags">
                                    <span class="tag">SMEP/SMAP</span>
                                    <span class="tag">KASLR</span>
                                    <span class="tag">KPTI</span>
                                </div>
                            </div>
                            <div class="technique-card">
                                <h4>Browser Exploitation</h4>
                                <p>Modern browser security bypass techniques</p>
                                <div class="technique-tags">
                                    <span class="tag">V8</span>
                                    <span class="tag">JIT</span>
                                    <span class="tag">Sandbox</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Challenge Browser -->
    <section class="challenges-section" id="challenges">
        <div class="container">
            <h2 class="section-title">CTF Challenge Browser</h2>
            <p class="section-subtitle">Browse and solve 75+ carefully curated CTF challenges with detailed solutions</p>

            <!-- Challenge Statistics -->
            <div class="challenge-stats">
                <div class="stat-card">
                    <span class="stat-value">75+</span>
                    <span class="stat-label">Total Challenges</span>
                </div>
                <div class="stat-card">
                    <span class="stat-value">9</span>
                    <span class="stat-label">Categories</span>
                </div>
                <div class="stat-card">
                    <span class="stat-value">3</span>
                    <span class="stat-label">Difficulty Levels</span>
                </div>
                <div class="stat-card">
                    <span class="stat-value">200+</span>
                    <span class="stat-label">Solving Methods</span>
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="challenge-filters">
                <button class="filter-btn active" data-filter="all">All Categories</button>
                <button class="filter-btn" data-filter="web">Web</button>
                <button class="filter-btn" data-filter="crypto">Crypto</button>
                <button class="filter-btn" data-filter="forensics">Forensics</button>
                <button class="filter-btn" data-filter="pwn">Pwn</button>
                <button class="filter-btn" data-filter="reverse">Reverse</button>
                <button class="filter-btn" data-filter="misc">Misc</button>
                <button class="filter-btn" data-filter="mobile">Mobile</button>
                <button class="filter-btn" data-filter="cloud">Cloud</button>
                <button class="filter-btn" data-filter="hardware">Hardware</button>
            </div>

            <!-- Difficulty Filter -->
            <div class="difficulty-filters">
                <button class="diff-filter-btn active" data-difficulty="all">All Levels</button>
                <button class="diff-filter-btn" data-difficulty="easy">Easy</button>
                <button class="diff-filter-btn" data-difficulty="medium">Medium</button>
                <button class="diff-filter-btn" data-difficulty="hard">Hard</button>
            </div>

            <!-- Search Bar -->
            <div class="search-container">
                <input type="text" id="challengeSearch" placeholder="Search challenges, tools, or techniques..." class="search-input">
                <ion-icon name="search-outline" class="search-icon"></ion-icon>
                <div class="search-suggestions" id="searchSuggestions"></div>
            </div>

            <!-- Challenge Grid -->
            <div class="challenges-grid" id="challengesGrid">
                <!-- Challenges will be populated by JavaScript -->
            </div>

            <!-- Load More Button -->
            <div class="load-more-container">
                <button class="btn btn-secondary" id="loadMoreBtn">Load More Challenges</button>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="container">
            <div class="newsletter-content">
                <h2>Stay Updated with Latest CTF Challenges</h2>
                <p>Get weekly updates on new challenges, writeups, and cybersecurity trends</p>
                <form class="newsletter-form" id="newsletterForm">
                    <input type="email" placeholder="Enter your email address" required>
                    <button type="submit" class="btn btn-primary">Subscribe</button>
                </form>
                <div class="newsletter-features">
                    <div class="feature">
                        <ion-icon name="mail-outline"></ion-icon>
                        <span>Weekly CTF Digest</span>
                    </div>
                    <div class="feature">
                        <ion-icon name="trophy-outline"></ion-icon>
                        <span>Competition Alerts</span>
                    </div>
                    <div class="feature">
                        <ion-icon name="book-outline"></ion-icon>
                        <span>New Writeups</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <!-- Footer Top Section -->
            <div class="footer-top">
                <div class="footer-cta">
                    <h2>Ready to Master CTF Challenges?</h2>
                    <p>Join thousands of cybersecurity professionals who trust Cyber Wolf for their CTF training</p>
                    <div class="footer-cta-buttons">
                        <a href="#challenges" class="btn btn-primary">Start Learning</a>
                        <a href="#community" class="btn btn-secondary">Join Community</a>
                    </div>
                </div>
            </div>

            <!-- Footer Main Content -->
            <div class="footer-content">
                <div class="footer-brand">
                    <a href="index.html" class="logo">
                        <div class="logo-container">
                            <img src="assets_I/logo/logo.jpeg" alt="Cyber Wolf Logo" class="logo-image">
                            <span class="logo-text">Cyber Wolf</span>
                        </div>
                    </a>
                    <p class="footer-description">
                        Advanced cybersecurity training and CTF challenge solving methodologies.
                        Master the art of ethical hacking through comprehensive hands-on challenges,
                        expert-crafted writeups, and cutting-edge tools.
                    </p>

                    <!-- Statistics -->
                    <div class="footer-stats">
                        <div class="footer-stat">
                            <span class="stat-number">75+</span>
                            <span class="stat-label">Challenges</span>
                        </div>
                        <div class="footer-stat">
                            <span class="stat-number">10K+</span>
                            <span class="stat-label">Students</span>
                        </div>
                        <div class="footer-stat">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">Tools</span>
                        </div>
                    </div>

                    <!-- Social Links -->
                    <div class="social-links">
                        <h5>Follow Us</h5>
                        <div class="social-icons">
                            <a href="https://twitter.com/cyberwolf" class="social-link" aria-label="Twitter" target="_blank">
                                <ion-icon name="logo-twitter"></ion-icon>
                                <span>Twitter</span>
                            </a>
                            <a href="https://github.com/cyberwolf" class="social-link" aria-label="GitHub" target="_blank">
                                <ion-icon name="logo-github"></ion-icon>
                                <span>GitHub</span>
                            </a>
                            <a href="https://discord.gg/cyberwolf" class="social-link" aria-label="Discord" target="_blank">
                                <ion-icon name="logo-discord"></ion-icon>
                                <span>Discord</span>
                            </a>
                            <a href="https://linkedin.com/company/cyberwolf" class="social-link" aria-label="LinkedIn" target="_blank">
                                <ion-icon name="logo-linkedin"></ion-icon>
                                <span>LinkedIn</span>
                            </a>
                            <a href="https://youtube.com/cyberwolf" class="social-link" aria-label="YouTube" target="_blank">
                                <ion-icon name="logo-youtube"></ion-icon>
                                <span>YouTube</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="footer-links">
                    <h4>Quick Navigation</h4>
                    <ul>
                        <li><a href="index.html"><ion-icon name="home-outline"></ion-icon>Home</a></li>
                        <li><a href="#ctf-methodology"><ion-icon name="library-outline"></ion-icon>Methodology</a></li>
                        <li><a href="#challenges"><ion-icon name="trophy-outline"></ion-icon>Challenges</a></li>
                        <li><a href="#tools"><ion-icon name="construct-outline"></ion-icon>Tools</a></li>
                        <li><a href="#writeups"><ion-icon name="document-text-outline"></ion-icon>Writeups</a></li>
                        <li><a href="#about"><ion-icon name="information-circle-outline"></ion-icon>About Us</a></li>
                    </ul>
                </div>

                <div class="footer-categories">
                    <h4>Challenge Categories</h4>
                    <ul>
                        <li><a href="#challenges" data-filter="web"><ion-icon name="globe-outline"></ion-icon>Web Exploitation</a></li>
                        <li><a href="#challenges" data-filter="crypto"><ion-icon name="lock-closed-outline"></ion-icon>Cryptography</a></li>
                        <li><a href="#challenges" data-filter="forensics"><ion-icon name="search-outline"></ion-icon>Digital Forensics</a></li>
                        <li><a href="#challenges" data-filter="pwn"><ion-icon name="bug-outline"></ion-icon>Binary Exploitation</a></li>
                        <li><a href="#challenges" data-filter="reverse"><ion-icon name="code-slash-outline"></ion-icon>Reverse Engineering</a></li>
                        <li><a href="#challenges" data-filter="mobile"><ion-icon name="phone-portrait-outline"></ion-icon>Mobile Security</a></li>
                        <li><a href="#challenges" data-filter="cloud"><ion-icon name="cloud-outline"></ion-icon>Cloud Security</a></li>
                        <li><a href="#challenges" data-filter="misc"><ion-icon name="extension-puzzle-outline"></ion-icon>Miscellaneous</a></li>
                    </ul>
                </div>

                <div class="footer-resources">
                    <h4>Learning Resources</h4>
                    <ul>
                        <li><a href="#lab"><ion-icon name="flask-outline"></ion-icon>Practice Lab</a></li>
                        <li><a href="#videos"><ion-icon name="play-circle-outline"></ion-icon>Video Tutorials</a></li>
                        <li><a href="#community"><ion-icon name="people-outline"></ion-icon>Community Forum</a></li>
                        <li><a href="#blog"><ion-icon name="newspaper-outline"></ion-icon>Security Blog</a></li>
                        <li><a href="#certification"><ion-icon name="ribbon-outline"></ion-icon>Certification Prep</a></li>
                        <li><a href="#downloads"><ion-icon name="download-outline"></ion-icon>Downloads</a></li>
                    </ul>
                </div>

                <div class="footer-contact">
                    <h4>Contact & Support</h4>
                    <div class="contact-info">
                        <div class="contact-item">
                            <ion-icon name="mail-outline"></ion-icon>
                            <div class="contact-details">
                                <span class="contact-label">Email</span>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                        </div>
                        <div class="contact-item">
                            <ion-icon name="logo-discord"></ion-icon>
                            <div class="contact-details">
                                <span class="contact-label">Discord</span>
                                <span>CyberWolf#1337</span>
                            </div>
                        </div>
                        <div class="contact-item">
                            <ion-icon name="time-outline"></ion-icon>
                            <div class="contact-details">
                                <span class="contact-label">Support</span>
                                <span>24/7 Community Support</span>
                            </div>
                        </div>
                        <div class="contact-item">
                            <ion-icon name="location-outline"></ion-icon>
                            <div class="contact-details">
                                <span class="contact-label">Location</span>
                                <span>Global Remote Team</span>
                            </div>
                        </div>
                    </div>

                    <!-- Newsletter Signup -->
                    <div class="footer-newsletter">
                        <h5>Stay Updated</h5>
                        <p>Get the latest CTF challenges and security news</p>
                        <form class="newsletter-form-footer">
                            <input type="email" placeholder="Your email address" required>
                            <button type="submit" class="btn btn-primary">
                                <ion-icon name="paper-plane-outline"></ion-icon>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Footer Middle Section -->
            <div class="footer-middle">
                <div class="footer-certifications">
                    <h4>Certifications & Partnerships</h4>
                    <div class="cert-logos">
                        <div class="cert-item">
                            <ion-icon name="shield-checkmark-outline"></ion-icon>
                            <span>CISSP Aligned</span>
                        </div>
                        <div class="cert-item">
                            <ion-icon name="school-outline"></ion-icon>
                            <span>CEH Compatible</span>
                        </div>
                        <div class="cert-item">
                            <ion-icon name="trophy-outline"></ion-icon>
                            <span>OSCP Prep</span>
                        </div>
                        <div class="cert-item">
                            <ion-icon name="ribbon-outline"></ion-icon>
                            <span>GCIH Ready</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom Section -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="footer-bottom-left">
                        <p>&copy; 2024 Cyber Wolf. All rights reserved.</p>
                        <p class="footer-version">Version 2.1.0 | Last updated: December 2024</p>
                    </div>
                    <div class="footer-bottom-center">
                        <div class="footer-badges">
                            <span class="badge-item">
                                <ion-icon name="shield-outline"></ion-icon>
                                Secure Platform
                            </span>
                            <span class="badge-item">
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                Verified Content
                            </span>
                            <span class="badge-item">
                                <ion-icon name="people-outline"></ion-icon>
                                Community Driven
                            </span>
                        </div>
                    </div>
                    <div class="footer-bottom-right">
                        <div class="footer-bottom-links">
                            <a href="#privacy">Privacy Policy</a>
                            <a href="#terms">Terms of Service</a>
                            <a href="#cookies">Cookie Policy</a>
                            <a href="#security">Security</a>
                            <a href="#accessibility">Accessibility</a>
                            <a href="#sitemap">Sitemap</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Challenge Modal -->
    <div class="modal" id="challengeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Challenge Details</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Challenge details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Progress Modal -->
    <div class="modal" id="progressModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Your Progress</h2>
                <span class="close-modal" onclick="closeModal('progressModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="progress-overview">
                    <div class="progress-stats">
                        <div class="progress-stat">
                            <span class="progress-number" id="completedChallenges">0</span>
                            <span class="progress-label">Completed</span>
                        </div>
                        <div class="progress-stat">
                            <span class="progress-number" id="totalPoints">0</span>
                            <span class="progress-label">Points Earned</span>
                        </div>
                        <div class="progress-stat">
                            <span class="progress-number" id="currentStreak">0</span>
                            <span class="progress-label">Day Streak</span>
                        </div>
                    </div>
                    <div class="progress-chart">
                        <canvas id="progressChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="category-progress">
                    <h3>Category Progress</h3>
                    <div class="category-progress-list" id="categoryProgressList">
                        <!-- Category progress will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <span class="close-modal" onclick="closeModal('settingsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Appearance</h3>
                    <div class="setting-item">
                        <label for="themeSelect">Theme:</label>
                        <select id="themeSelect">
                            <option value="dark">Dark</option>
                            <option value="light">Light</option>
                            <option value="auto">Auto</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="fontSizeSlider">Font Size:</label>
                        <input type="range" id="fontSizeSlider" min="12" max="20" value="16">
                        <span id="fontSizeValue">16px</span>
                    </div>
                </div>
                <div class="settings-section">
                    <h3>Notifications</h3>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="emailNotifications" checked>
                            Email notifications for new challenges
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="progressNotifications" checked>
                            Progress milestone notifications
                        </label>
                    </div>
                </div>
                <div class="settings-section">
                    <h3>Data</h3>
                    <div class="setting-item">
                        <button class="btn btn-secondary" onclick="exportProgress()">Export Progress</button>
                        <button class="btn btn-danger" onclick="resetProgress()">Reset All Progress</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Buttons -->
    <div class="floating-actions">
        <button class="fab main-fab" onclick="toggleFabMenu()">
            <ion-icon name="add-outline"></ion-icon>
        </button>
        <div class="fab-menu" id="fabMenu">
            <button class="fab" onclick="openModal('progressModal')" title="View Progress">
                <ion-icon name="stats-chart-outline"></ion-icon>
            </button>
            <button class="fab" onclick="openModal('settingsModal')" title="Settings">
                <ion-icon name="settings-outline"></ion-icon>
            </button>
            <button class="fab" onclick="toggleBookmarks()" title="Bookmarks">
                <ion-icon name="bookmark-outline"></ion-icon>
            </button>
            <button class="fab" onclick="randomChallenge()" title="Random Challenge">
                <ion-icon name="shuffle-outline"></ion-icon>
            </button>
        </div>
    </div>

    <!-- Notification Toast -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <ion-icon name="checkmark-circle-outline"></ion-icon>
            <span class="toast-message">Action completed successfully!</span>
        </div>
        <button class="toast-close" onclick="hideToast()">
            <ion-icon name="close-outline"></ion-icon>
        </button>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading challenges...</p>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        <ion-icon name="chevron-up-outline"></ion-icon>
    </button>

    <!-- JavaScript Functions -->
    <script>
        // Toggle technique accordion
        function toggleTechnique(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('ion-icon');

            if (content.style.display === 'block') {
                content.style.display = 'none';
                icon.setAttribute('name', 'chevron-down-outline');
            } else {
                content.style.display = 'block';
                icon.setAttribute('name', 'chevron-up-outline');
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // FAB menu toggle
        function toggleFabMenu() {
            const fabMenu = document.getElementById('fabMenu');
            fabMenu.classList.toggle('active');
        }

        // Utility functions
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const toastMessage = toast.querySelector('.toast-message');
            const toastIcon = toast.querySelector('ion-icon');

            toastMessage.textContent = message;

            // Update icon based on type
            switch(type) {
                case 'success':
                    toastIcon.setAttribute('name', 'checkmark-circle-outline');
                    break;
                case 'error':
                    toastIcon.setAttribute('name', 'alert-circle-outline');
                    break;
                case 'info':
                    toastIcon.setAttribute('name', 'information-circle-outline');
                    break;
            }

            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        function hideToast() {
            document.getElementById('toast').classList.remove('show');
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Show/hide back to top button
        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    </script>

    <script src="blog.js"></script>
</body>
</html>
